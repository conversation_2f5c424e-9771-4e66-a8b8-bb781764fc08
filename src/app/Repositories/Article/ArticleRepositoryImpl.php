<?php

namespace App\Repositories\Article;

use App\Models\Prediction;
use App\Repositories\Parliament\ParliamentRepository;
use App\Repositories\Video\VideoRepository;
use App\Services\Newswav\UserProfile\UserProfileService;
use App\Services\PollWav\PollService\PollService;
use App\Services\Newswav\SarawakElection\SarawakElectionService;
use App\V4\Models\Article;
use App\V4\Models\ArticleContent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Elasticsearch\ClientBuilder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Swoole\Coroutine\Channel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use App\Repositories\Post\PostRepository;
use App\Repositories\Publisher\PublisherRepository;
use App\Repositories\Topic\TopicRepository;
use App\Services\Newswav\Pin\PinItemService;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use App\Repositories\Bookmark\BookmarkRepository;
use App\Repositories\History\HistoryRepository;
use App\Repositories\Report\ReportRepository;
use App\Services\Newswav\MelakaElection\MelakaElectionService;
use App\Services\Newswav\MelakaElection\MelakaElectionServiceImpl;
use App\Services\Newswav\Olympic\OlympicService;
use Exception;
use App\Services\Newswav\SarawakElection\SarawakElectionServiceImpl;
use App\V4\Exceptions\NwInvalidSearchKeyword;
use Elasticsearch\Common\Exceptions\BadRequest400Exception;
use Newswav\NewswavAuth\Facade\NewswavAuth;
use App\Models\FbUser;
use App\Models\NwUser;
use App\V4\Models\NwUser as NwUserV4;
use App\V4\Models\NwWebUser;


class ArticleRepositoryImpl implements ArticleRepository
{
    protected $limit = 30;
    protected $articleMode = 1;
    protected $briefMode = false;
    protected $designVersion = 0;
    protected $removeSuspectCorona = false;
    protected $imageproxykey;
    protected $thumbnailsquare = 500;
    protected $thumbnailwidth = 800;
    protected $thumbnailheight = 450;
    protected $thumbnailquality = 50; //set compression quality to 50%. example values: 10 = 10% quality compression, 50 = 50% compression, 100 = 100% quality (maintain the quality)
    protected $importantPublishers;
    protected $excludeFromHotPublishers;


    public function __construct()
    {
        $this->imageproxykey = config('app.image_proxy_key'); //get the image proxy key for url signing in environment variable
        $this->imageproxysquare = config('app.image_proxy_square'); // for 1:1 ratio thumbnail
        $this->imageproxywidth = config('app.image_proxy_width');  //16:9 width
        $this->imageproxyheight = config('app.image_proxy_height'); // 16:9 height
        $this->thumbnailquality = config('app.image_proxy_quality'); // qualty compression

        $this->importantPublishers = nw_bunker('publisher', 'important_publishers'); // [263,262,20,96,97,101,409]
    }

    public function removeSuspectFromCovid(): void
    {
        $this->removeSuspectCorona = true;
    }

    public function setDesignVersion($version): void
    {
        $this->designVersion = $version;
    }

    public function setFeedPerPage($feedPerPage): void
    {
        $this->limit = $feedPerPage;
    }

    public function setBriefMode($briefMode): void
    {
        $this->briefMode = $briefMode;
    }

    public function setArticleMode($articleMode): void
    {
        $this->articleMode = $articleMode;
    }

    public function getSelectArticlesQuery(
        array $languages,
        ?string $lastID,
        ?string $firstID,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $requestMode = null,
        ?string $sortBy = null,
        ?string $firstData = null,
        ?string $lastData = null
    ) {
        $query = DB::table('predictions')
            ->leftJoin('article_stats','predictions.unique_id', "=", 'article_stats.article_id')
            ->select('predictions.unique_id', 'predictions.published_date', 'predictions.channel_id', 'article_stats.score')
            ->distinct()
            ->whereIn('predictions.language', $languages);

        if($sortBy === 'score'){
            $query = $query->where('predictions.published_date', '<', date('Y-m-d H:i:s'));
            if ($lastData && $requestMode === 'older') {
                $query = $query
                    ->where('article_stats.score', '<', $lastData)
                    ->orderByDesc('article_stats.score');
            } elseif ($firstData && $requestMode === 'newer') {
                $query = $query
                    ->where('article_stats.score', '>', $firstData)
                    ->orderByDesc('article_stats.score');
            } else {
                $query = $query->orderByDesc('article_stats.score');
            }
        }
        else{
            if ($lastID) {
                $query = $query
                    ->where('published_date', "<", $this->getNextArticleDateFromBottom($lastID))
                    ->orderBy('published_date', 'DESC');
            } elseif ($firstID) {
                $query = $query
                    ->where('published_date', ">", $this->getNextArticleDateFromTop($firstID))
                    ->where('published_date', "<", date('Y-m-d H:i:s'))
                    ->orderBy('published_date', 'DESC');
            } else if($startDate && $requestMode == 'newer') {
                $query = $query
                    ->where('published_date', ">",$startDate)
                    ->where('published_date', "<", date('Y-m-d H:i:s'))
                    ->orderBy('published_date', 'DESC');
            } else if($endDate && $requestMode == 'older') {
                $query = $query
                    ->where('published_date', "<",$endDate)
                    ->orderBy('published_date', 'DESC');
            } else {
                $query = $query
                    ->where('published_date', "<", date('Y-m-d H:i:s'))
                    ->orderBy('published_date', 'DESC');
            }
        }

        $firebaseId = Auth::user()->getFirebaseId();
        if($firebaseId){
            $excludedUniqueIds = [];
            //reported contents
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if($reportedArticles && count($reportedArticles)){
                $excludedUniqueIds = array_merge($excludedUniqueIds, $reportedArticles);
            }

            /**
             * user not interested contents
             * no join/subquery - Illegal mix of collations (utf8mb4_unicode_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='
             * will be using query -> pluck unique ids -> whereNotIn query
             */
            /** @var UserProfileservice $userService */
            $userService = app(UserProfileService::class);
            $hiddenContents = $userService->getHiddenContentList($firebaseId);
            $excludedUniqueIds = array_merge($excludedUniqueIds, $hiddenContents);

            if (count($excludedUniqueIds) > 0) {
                $query = $query->whereNotIn('unique_id', array_unique($reportedArticles));
            }
        }
        return $query;
    }

    public function getArticle(string $articleUniqueId)
    {
        try {
            $filterArticle = nw_bunker('article', 'filter');

            if(in_array($articleUniqueId, $filterArticle)){
                abort(404,"Article not found");
            }
            if (like_match('A%_P_%', $articleUniqueId)) {
                $pollService = app(PollService::class);
                return  $pollService->getPollByArticleId($articleUniqueId, null);
            }

            $article = $this->getArticles([$articleUniqueId], true);


            if (isset($article->publisher->id)){
                $checkquery = DB::table('publishers')
                ->where('id', $article->publisher->id)
                ->where('enabled', 0)
                ->get();

                if (count($checkquery)>0){
                    return null;
                }
            }

            return $article;
        } catch(\Symfony\Component\HttpKernel\Exception\NotFoundHttpException $e){
            throw $e;
        } catch (\Exception $e) {
            notify_now($e);
            return $this->getArticles([$articleUniqueId], true);
        }
    }

    public function getFullArticles(array $articleUniqueIds, $compact = false)
    {
        if ($compact) {
            return $this->getBriefArticles($articleUniqueIds, false, true);
        }
        return $this->getArticles($articleUniqueIds, false, true);
    }

    public function getHotArticles(
        array $languages,
        int $page
    ): array {
        $pinService = app(PinItemService::class);
        $userProfileService = app(UserProfileService::class);

        // filter out muted and disabled channels
        $excludedChannelIds = $userProfileService->getMutedChannels();
        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $excludedChannelIds = array_merge($excludedChannelIds, explode(',', $disabledChannels));
        }

        // filter out pinned, covid data and reported articles
        $pinnedArticles = $pinService->getPinned('F_F_-2', ['article', 'video']);
        $excludedUniqueIds = array_merge($pinnedArticles, nw_bunker('article', 'covid', []));
        $firebaseId = Auth::user()->getFirebaseId();
        if($firebaseId){
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if($reportedArticles && count($reportedArticles)){
                $excludedUniqueIds = array_merge($excludedUniqueIds, $reportedArticles);
            }
        }

        $query = DB::table('predictions')
            ->select(DB::raw('score, unique_id, published_date, channel_id'))
            ->distinct()
            ->whereBetween('published_date', [Carbon::now()->subHours(16), Carbon::now()])
            ->whereIn('language', $languages)
            ->orderByDesc('score')
            ->take($this->limit);

        if ($page > 1) {
            $query = $query->offset((($page - 2) * 20) + 10);
        }
        $queryData = $query->get();

        // filter out excluded unique ids and channel ids
        $ids = $queryData
            ->reject(function ($article) use ($excludedUniqueIds, $excludedChannelIds) {
                return in_array($article->unique_id, $excludedUniqueIds)
                    || in_array($article->channel_id, $excludedChannelIds);
            })
            ->pluck('unique_id')
            ->values()
            ->toArray();

        if (count($pinnedArticles) > 0 && $page <= 1) {
            return $this->getArticles($ids, false, true , [], false, false, $pinnedArticles);
        }
        return $this->getArticles($ids, false, true );
    }


    public function getHotArticlesWeb(
        array $languages,
        int $page
    ) {

        $pinService = app(PinItemService::class);

        $pinnedArticles = $pinService->getPinned('F_F_-2', ['article', 'video']);



        $query = DB::table('predictions')
            ->select(DB::raw('score, unique_id, published_date'))
            ->distinct()
            ->where('created_at', '<=', Carbon::now())
            ->where(function ($query){
                $query->where('created_at', '>=', Carbon::now()->subHours(24))
                ->orWhere(function($q){
                    $q->whereIn('publisher_id', $this->importantPublishers);
                })
                 ->orWhere(function($q){
                    $q->where('article_url','like', "%newswavmaker%");
                });
            })
            ->where('published_date', '<=', Carbon::now())
            ->whereNotIn('channel_id', [379])
            ->where('published_date', '>=', Carbon::now()->subHours(16))
            ->whereIn('language', $languages)
            ->orderByDesc('score');


        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
        }
        //avoid fetching the pinned unique_ids
        $query = $query->whereNotIn('unique_id', $pinnedArticles);

        $articles = json_decode($query->take($this->limit)->simplePaginate($this->limit)->appends(['languages'=>implode(",", $languages)])->toJson());


        if ($page > 10){
            $articles->data = [];
            $articles->next_page_url = str_replace("page=1","page=10",urldecode($articles->first_page_url));
        }else{
            $ids = array();
            foreach ($articles->data as $d){
                $ids[] = $d->unique_id;
            }

            if ($page <= 1){
                if ($pinnedArticles && count($pinnedArticles) > 0) {

                    //$merge = array_unique(array_merge($pinnedArticles, $ids));
                    //$articles = $this->getArticles($merge, false, true );

                    $articles->data = $this->getArticlesWeb($ids, false, true , [], false, false, $pinnedArticles);

                    //abort(400, "in");

                }else{
                    $articles->data = $this->getArticlesWeb($ids, false, true);
                }
            }else{
                $articles->data = $this->getArticlesWeb($ids, false, true);
            }
            $articles->next_page_url = urldecode($articles->next_page_url);
        }





        //default url generated is ugly :(
        $articles->first_page_url = urldecode($articles->first_page_url);
        $articles->last_page_url = str_replace("page=1","page=10",urldecode($articles->first_page_url));

        $articles->prev_page_url = urldecode($articles->prev_page_url);
        $articles->last_page = 10;




        return $articles;

    }

    public function getPublisherArticlesWeb(
        array $languages,
        int $page,
        int $publisher_id,
        $lastID,
        $firstID
    ) {

        $query = $this->getSelectArticlesQuery(
            $languages,
            $lastID,
            $firstID, null,null
        );




        $query->where('publisher_id', $publisher_id);
        //if (!(is_array($publishers) && count($publishers) == 1 && (!$topics || count($topics) == 0))) {
            $disabledChannels = getDisabledChannelsFromCache();
            if ($disabledChannels) {
                $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
            }
       // }



        $articles = json_decode($query->take($this->limit)->simplePaginate(20)->appends(['languages'=>implode(",", $languages)])->toJson());


        if ($page > 10){
            $articles->data = [];
            $articles->next_page_url = str_replace("page=1","page=10",urldecode($articles->first_page_url));
        }else{
            $ids = array();
            foreach ($articles->data as $d){
                $ids[] = $d->unique_id;
            }
            $articles->data = $this->getArticlesWeb($ids, false, true);
            $articles->next_page_url = urldecode($articles->next_page_url);
        }





        //default url generated is ugly :(
        $articles->first_page_url = urldecode($articles->first_page_url);
        $articles->last_page_url = str_replace("page=1","page=10",urldecode($articles->first_page_url));

        $articles->prev_page_url = urldecode($articles->prev_page_url);
        $articles->last_page = 10;

        if (app()->env != "local"){
            $articles->next_page_url = str_replace("http","https",urldecode($articles->next_page_url));
            $articles->prev_page_url = str_replace("http","https",urldecode($articles->prev_page_url));
        }



        return $articles;

    }

    public function getFollowingArticles(
        ?array $topics,
        ?array $languages,
        ?array $publishers,
        ?string $lastID,
        ?string $firstID
    ): array {
        $query = $this->getSelectArticlesQuery(
            $languages,
            $lastID,
            $firstID, null,null
        );



        if ($topics || $publishers) {
            $query->where(function ($query) use ($topics, $publishers) {
                if ($topics) {
                    $query = $query->whereIn('topic', $topics);
                }

                if ($publishers) {
                    $query = $query->orWhereIn('publisher_id', $publishers);
                }
            });
        }


        $excludedChannelIds = [];
        if (count($topics ?? []) > 0 || count($publishers ?? [])  > 1) {
            $service = app(UserProfileService::class);
            $channels = $service->getMutedChannels();
            if ($channels && count($channels) > 0){
                $excludedChannelIds = array_merge($excludedChannelIds, $channels);
            }
        } else if (count($publishers ?? []) == 1 && $publishers[0] == 369) {
            return [];
        }

        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $disabledChannelIds = array_merge($excludedChannelIds, explode(',', $disabledChannels));
        }

       $pinService = app(PinItemService::class);
       $path = app('request')->getPathInfo();
       $pinnedArticles = [];
       if (Str::contains($path, '/topic')){ //check if topic feed
        try{
            $findTopic = ($topics[0]);
            $pinnedArticles = $pinService->getPinned('F_T_'.$findTopic, ['article', 'video']);
        }catch(Exception $e){
            //incase we failed to get the topic
            //do nothing
        }
       }
       if (Str::contains($path, 'following')){ //or following feed
        $pinnedArticles = $pinService->getPinned('F_F_-1', ['article', 'video']);
       }

        $results = $query
            ->take($this->limit)
            ->get()
            ->whereNotIn('channel_id', $disabledChannelIds)
            ->whereNotIn('publisher_id', [nw_bunker('poll', 'pollwav_publisher_id', 369)])
            ->pluck('unique_id')
            ->toArray();
        $results = array_diff($results, $pinnedArticles);
        if (count($pinnedArticles) > 0){
            if (!$lastID && !$firstID){
                //first request, insert pin articles
                return $this->getArticles($results, false, true, $topics, false, false, $pinnedArticles);
            }
        }

        return $this->getArticles($results, false, true, $topics);
    }

    public function getFollowingArticlesWeb(
        ?array $topics,
        ?array $languages,
        ?array $publishers,
        ?string $lastID,
        ?string $firstID,
        ?int $page
    ) {
        if ($publishers){
            if (count($publishers)==1){

                $checkquery = DB::table('publishers')
                ->whereIn('id', $publishers)
                ->where('enabled', 0)
                ->get();

                if (count($checkquery)>0){
                    return [];
                }
            }
        }


        $query = $this->getSelectArticlesQuery(
            $languages,
            $lastID,
            $firstID, null,null
        );

        $appendarray = array();


        // if ($topics || $publishers) {
        //     $query->where(function ($query) use ($topics, $publishers, &$appendarray) {
        //         if ($topics) {
        //             $query = $query->whereIn('topic', $topics);
        //             if (count($topics)> 0){
        //                 //$appendarray += ['topics'=>implode(",", $topics)];
        //             }
        //         }
        //         if ($publishers) {
        //             $query = $query->orWhereIn('publisher_id', $publishers);
        //             if (count($publishers)> 0){
        //                 //$appendarray += ['publishers'=>implode(",", $publishers)];
        //             }
        //         }
        //     });
        // }



        if (count($topics ?? []) > 0 || count($publishers ?? [])  > 1) {
            $service = app(UserProfileService::class);
            $channels = $service->getMutedChannels();
            if ($channels && count($channels) > 0)
                $query = $query->whereNotIn('channel_id', $channels);
        } else if (count($publishers ?? []) == 1 && $publishers[0] == 369) {
            return [];
        }

        //if (!(is_array($publishers) && count($publishers) == 1 && (!$topics || count($topics) == 0))) {
            $disabledChannels = getDisabledChannelsFromCache();
            if ($disabledChannels) {
                $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
            }
       // }




        if (count($languages)> 0){
            $appendarray += ['languages'=>implode(",", $languages)];
        }



        $articles = json_decode($query->take($this->limit)->simplePaginate($this->limit)->appends($appendarray)->toJson());



        if ($page > 10){
            $articles->data = [];
            $articles->next_page_url = str_replace("page=1","page=10",urldecode($articles->first_page_url));
        }else{
            $ids = array();
            foreach ($articles->data as $d){
                $ids[] = $d->unique_id;
            }
            $articles->data = $this->getArticlesWeb($ids, false, true, $topics);
            $articles->next_page_url = urldecode($articles->next_page_url);
        }


        //default url generated is ugly :(
        $articles->first_page_url = urldecode($articles->first_page_url);
        $articles->last_page_url = str_replace("page=1","page=10",urldecode($articles->first_page_url));

        $articles->prev_page_url = urldecode($articles->prev_page_url);
        $articles->last_page = 10;




        return $articles;
    }


    public function getFollowingArticlesIds(
        ?array $topics,
        ?array $languages,
        ?array $publishers,
        ?string $lastDate,
        ?string $firstDate,
        ?string $requestMode,
        ?array $exclude = [],
        ?string $sortBy = null,
        ?string $firstData = null,
        ?string $lastData = null
    ){

        $query = $this->getSelectArticlesQuery(
            $languages,
            null,
            null,
            $firstDate,
            $lastDate,
            $requestMode,
            $sortBy,
            $firstData,
            $lastData
        );

        $query->when(count($exclude) > 0, function ($query) use ($exclude) {
            $query->whereNotIn('unique_id', $exclude);
        });

        if ($topics || $publishers) {
            $query->where(function ($query) use ($topics, $publishers) {
                if ($topics) {
                    $query = $query->whereIn('topic', $topics);
                }

                if ($publishers) {
                    $query = $query->orWhereIn('publisher_id', $publishers);
                }
            });
        }

        $data = $query->take($this->limit)->get()->toArray();

        // filter out disabled channels
        $excludedChannelIds = explode(',', getDisabledChannelsFromCache());
        if (count($topics ?? []) > 0 || count($publishers ?? [])  > 1) {
            $service = app(UserProfileService::class);
            $excludedChannelIds = array_merge($excludedChannelIds, $service->getMutedChannels());
        } else if (count($publishers ?? []) == 1 && $publishers[0] == 369) {
            return [];
        }
        $data = array_filter($data, function ($item) use($excludedChannelIds) {
            return in_array($item->channel_id, $excludedChannelIds) === false;
        });

        $firstData = null;
        $lastData = null;

        if (!empty($data)) {
            $firstElement = reset($data);
            $lastElement = end($data);

            $firstData = $firstElement->score ?? null;
            $lastData = $lastElement->score ?? null;

            if ($requestMode === 'newer') {
                [$firstData, $lastData] = [$lastData, $firstData];
            }
        }

        return collect([
            'data' => $data,
            'firstData' => $firstData,
            'lastData' => $lastData,
        ]);
    }

    public function getWidgetArticles(
        ?array $topics,
        ?array $languages,
        ?array $publishers
    ): array {
        $query = $this->getSelectArticlesQuery($languages, null, null, null, null);

        if ($topics || $publishers) {
            $query->where(function ($query) use ($topics, $publishers) {
                if ($topics) {
                    $query = $query->whereIn('topic', $topics);
                }

                if ($publishers) {
                    $query = $query->orWhereIn('publisher_id', $publishers);
                }
            });
        }


        $excludedChannelIds = [];
        if (count($topics ?? []) > 0 || count($publishers ?? [])  > 1) {
            $service = app(UserProfileService::class);
            $channels = $service->getMutedChannels();
            if (count($channels) > 0) {
                $excludedChannelIds = array_merge($excludedChannelIds, $channels);
            }
        } else if (count($publishers ?? []) == 1 && $publishers[0] == 369) {
            return [];
        }

        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $excludedChannelIds = array_merge($excludedChannelIds, explode(',', $disabledChannels));
        }
        return $query
            ->take(10)
            ->get()
            ->whereNotIn('channel_id', $excludedChannelIds)
            ->pluck('unique_id')
            ->toArray();
    }

    public function getForYouArticles(
        array $topics,
        array $languages,
        string $plaform,
        ?string $rawProfileId,
        ?array $shownArticles
    ): array {
        $limit = $this->limit;
        $ids = array();

        $pid = ($plaform == 'android' ? 'a_' : 'i_') . $rawProfileId;
        $excludeArticles = $rawProfileId ?  $this->getExcludedArticles($pid) : [];
        $mergedArray = array_merge($excludeArticles, $shownArticles ?? []);
        $service = app(UserProfileService::class);
        $muteChannels = $service->getMutedChannels();
        $firebaseId = Auth::user()->getFirebaseId();
        $results = DB::table('discoverArticlePoolV3')
            ->distinct()
            ->select('articleID')
            ->whereIn('language', $languages)
            ->whereIn('segmentTopicID', $topics)
            ->whereNotIn('articleID', $mergedArray)
            ->whereNotIn('channel_id', $muteChannels)
            ->when($firebaseId, function($query) use($firebaseId) {
                $reportRepo = app(ReportRepository::class);
                $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
                if($reportedArticles && count($reportedArticles)){
                    $query = $query->whereNotIn('articleID', $reportedArticles );
                }
                return $query;
            })
            ->orderBy('orderID')
            ->limit($limit)
            ->get()
            ->toArray();
        if (count($results) < $limit) {
            $results = DB::table('discoverArticlePoolV3')
                ->distinct()
                ->select('articleID')
                ->whereIn('language', $languages)
                ->whereIn('segmentTopicID', $topics)
                ->whereNotIn('articleID', $shownArticles ?? [])
                ->orderBy('orderID')
                ->limit($limit)
                ->get()
                ->toArray();
        }
        foreach ($results as $res) {
            $ids[] = $res->articleID;
        }
        $topicString = $this->arrayToString($topics);

        $pinService = app(PinItemService::class);
        $pinnedArticles = $pinService->getPinned('F_F_-3', ['article', 'video']);
        if (count($pinnedArticles)>0){
            $ids = array_diff($ids, $pinnedArticles);
            if (app('request')->input('pageNumber') == 0){
                return $this->getArticles($ids, false, true , $topicString, false, false, $pinnedArticles);
            }
        }
        return $this->getArticles($ids, false, true, $topicString);
    }


    public function getNextArticleDateFromTop($firstID)
    {
        $v = DB::table('predictions')
            ->select('published_date')
            ->where('article_id', $firstID)
            ->value('published_date');

        if(!$v){
            DB::beginTransaction();
            $v = DB::table('predictions')
            ->select('published_date')
            ->where('article_id', $firstID)
            ->value('published_date');
            DB::commit();
          //  Bugsnag::notifyError("Article pagination", "missed article id $firstID , $v ");
        }
        return $v ?? Carbon::now()->timestamp;

    }

    public function getNextArticleDateFromBottom($lastID)
    {
        $v = DB::table('predictions')
            ->select('published_date')
            ->where('article_id', $lastID)
            ->value('published_date');

            if(!$v){
                DB::beginTransaction();
                $v = DB::table('predictions')
                ->select('published_date')
                ->where('article_id', $lastID)
                ->value('published_date');
                DB::commit();
                Bugsnag::notifyError("Article pagination", "missed article id $lastID , $v ");
            }
        return $v ?? Carbon::now()->timestamp;
    }


    public function getEntityPublishedDate($uniqueId)
    {
        if (isVideo($uniqueId)) {
            $v =  DB::table('videos')
                ->select('published_date')
                ->where('unique_id', $uniqueId)
                ->value('published_date') ?? 0;
            if (!$v) {
                log_emergency("unexpected case, video not found $uniqueId");
                return Carbon::now();
            }
            return Carbon::createFromTimestamp($v)->toDateTimeString();
        } else if (isPodCast($uniqueId)) {
            $v = DB::table('podcasts')
                ->select('published_date')
                ->where('unique_id', $uniqueId)
                ->value('published_date') ?? 0;
            if (!$v) {
                log_emergency("unexpected case, video not found $uniqueId");
                return Carbon::now();
            }
            return Carbon::createFromTimestamp($v)->toDateTimeString();
        }
        $v = DB::table('predictions')
            ->select('published_date')
            ->where('unique_id', $uniqueId)
            ->value('published_date');
        return $v;
    }

    public function arrayToString($arr): string
    {
        $st = "";
        foreach ($arr as $a) {
            if ($a && $a != '')
                $st = $st . "'$a',";
        }
        $st = trim($st, ',');
        return $st;
    }

    public function searchTopics(string $q, ?string $lastID): array
    {
        $topicsRepo = app(TopicRepository::class);
        return $topicsRepo->searchTopics($q, $lastID);
    }

    public function searchPublishers(string $q, ?string $lastID): array
    {
        $publisherRepo = app(PublisherRepository::class);

        return $publisherRepo->searchPublishers($q, $lastID);
    }

    public function getHotTopics(): array
    {
        $topicsRepo = app(TopicRepository::class);
        return $topicsRepo->getHotTopics();
    }

    public function getHotPublishers($language): array
    {
        $publisherRepo = app(PublisherRepository::class);

        return $publisherRepo->getHotPublishers($language);
    }

    public function getHotSearchArticles($language, $onlyIds = false, $limit = 3): array
    {
        $query = DB::table('predictions')
            ->select(DB::raw('score, unique_id, published_date, channel_id'))
            ->distinct()
            ->where('published_date', '<=', Carbon::now())
            ->where('published_date', '>=', Carbon::now()->subHours(8))
            ->where('language', $language)
            ->orderByDesc('score');

        $excludedUniqueIds = [];
        $firebaseId = Auth::user()->getFirebaseId();
        if($firebaseId){
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if($reportedArticles && count($reportedArticles)){
                $excludedUniqueIds = array_merge($excludedUniqueIds, $reportedArticles);
            }
        }
        $excludedChannelIds = [];
        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $excludedChannelIds = array_merge($excludedChannelIds, explode(',', $disabledChannels));
        }

        $limit = $onlyIds ? $limit : 10;
        $data = $query
            ->take($limit)
            ->get()
            ->whereNotIn('unique_id', $excludedUniqueIds)
            ->whereNotIn('channel_id', $excludedChannelIds)
            ->pluck('unique_id')
            ->toArray();

        return $onlyIds ? $data : $this->getArticles($data, false, true);
    }


    public function getRelatedArticlesToArticle(string $articleUniqueId): ?array
    {
        $article = DB::table('article')
            ->where('uniqueID', $articleUniqueId)
            ->get()
            ->first();
        if (!$article) {
            return null;
        }
        if (
            !$article->relatedArticle ||
            trim($article->relatedArticle) == "" ||
            $article->relatedArticle == " "
        ) {
            return [];
        }
        $ids = explode(",", $article->relatedArticle);
        $ids2 = [];
        foreach($ids as $id){
            if(!like_match('A%_P_%', $id)){
                $ids2[] = $id;
            }
        }
        $articles = $this->getArticles($ids2, false, true);
        //filter disabled publishers
        return array_values(array_filter($articles, function ($item) {
            if($item->publisher->enabled == 0) {
                return false;
            }
            return true;
        }));
    }

    public function getDigestArticles(string $userId, string $profileId, string $platform, array $languages, $latestDate = null): ?array
    {
        $query = DB::table('articles_digest')
            ->where('user_id', $userId)
            ->limit(1)
            ->orderByDesc('digest_date');

        if ($latestDate != null) {
            $latestDate = Carbon::parse($latestDate)->subDay()->format('Y-m-d');
        } else {
            if (Carbon::now()->format('H') >= 20)
                $latestDate = Carbon::now()->format("Y-m-d");
            else
                $latestDate = Carbon::now()->subDay()->format("Y-m-d");
        }
        $query =  $query->where('digest_date', '=', $latestDate);
        $ids = false;
        $row = $query->first();
        if (!$row || !$row->articles || $row->articles == '') {
            $row = $this->getDefaultDigest($languages, $latestDate);
        }

        if (!$row) {
            return null;
        }
        $ids = explode(",", $row->articles);
        $arr = [];
        foreach ($ids as $id) {
            if (($id == 'covid19__en' || $id == 'covid19__zh' || $id == 'covid19__ms') && $platform == 'android') continue;
            $arr[] = $id;
        }
        $res = [
            "date" => $row->digest_date,
            "articles" => $this->getArticles($arr, false, true)
        ];
        foreach ($res['articles'] as $article) {
            $article->comments = $this->getComments($article->uniqueID, $profileId, $platform, null)['content'];
            $article->userReactionType = (string) $this->getUserReaction($article->uniqueID, $profileId, $platform);
        }
        return $res;
    }


    public function getComments(string $articleId, string $profileId, string $platform, ?string $lastCommentId)
    {
        $response = [];
        $tableName =  $platform == 'android' ? "commentLikeHistory_android" : "commentLikeHistory";
        $comments =  DB::select(
            "SELECT `comment`.`id`,`comment`.`createdDate`,`comment`.`updatedDate`,
            `comment`.`articleID`, `comment`.`userID`,`comment`.`content`,
            `comment`.`likeCount`,`comment`.`replyCount`,`comment`.`replyToCommentID`,
        ul.loginDisplayName as username,
        ul.loginPhotoURL as profileImageURL,
        IF( a.id is null, '0', a.isLike ) AS userIsLike
        FROM `comment`

        LEFT JOIN userLogin as ul
        ON ul.id = `comment`.userID

        LEFT JOIN
            (SELECT * FROM $tableName as a
            WHERE a.profileID = $profileId
            ORDER BY a.id DESC LIMIT 1) AS a
        ON `a`.`commentID` = `comment`.`id`

        AND `a`.`profileID` = $profileId
        WHERE `comment`.`articleID` = '$articleId'
        ORDER BY `id` DESC
        LIMIT 20"
        );

        $response['count'] = DB::table('comment')
            ->where('articleID', $articleId)
            ->count();
        $response['content'] = $comments;

        int_to_string_object($response);
        return $response;
    }

    public function getUserReaction(string $articleId, string $profileId, string $platform)
    {
        $pid = $platform == 'android' ? "a_$profileId" : "i_$profileId";
        return DB::table("user_entity_reaction_history")
            ->where('pid', $pid)
            ->where('entity_type', 'a')
            ->where('entity_id', $articleId)
            ->where('deleted', 0)
            ->limit(1)
            ->value('reaction_type') ?? 0;
    }

    private function getDefaultDigest($languages, $latestDate = null)
    {
        $query = DB::table('articles_default_digest')
            ->limit(1)
            ->orderByDesc('digest_date');
        if ($latestDate != null) {
            $query =  $query->where('digest_date', '=', $latestDate);
        }
        $row = $query->first();
        if (!$row || !$row->articles || $row->articles == '') {
            return null;
        }
        $ids = explode(",", $row->articles);
        $idsString = $this->arrayToString($ids);
        $articles = \implode(
            ',',
            DB::table('predictions')
                ->select('unique_id')
                ->distinct()
                ->whereIn('unique_id', $ids)
                ->whereIn('language', $languages)
                ->orderByRaw("FIELD (predictions.unique_id, $idsString)")
                ->limit(5)
                ->pluck('unique_id')
                ->toArray()
        );

        return (object) [
            'articles' => $articles,
            'digest_date' => $row->digest_date
        ];
    }

    public function getDisabledChannelsFromCache()
    {
        return getDisabledChannelsFromCache();
    }

    private function getDisabledChannels()
    {
        $results = DB::select('SELECT listing_enabled, GROUP_CONCAT(id) as d
        FROM channels
        where listing_enabled = ?
        GROUP by listing_enabled', [0]);
        if (count($results) != 1) return false;
        return $results[0]->d;
    }

    public function searchArticlesV3(string $q, array $languages, ?string $page): array
    {
        $disabledChannels = getDisabledChannelsFromCache();
        $pageSize = ((int) $page > 1) ? 30 : 10;
        $q = str_replace(":","", $q);
        $parts = $this->getSearchQuery($q);
        if (!count($parts)) {
            return $this->getHotSearchArticles($languages);
        }
        $ids = $this->searchArticlePerQuery($parts, $languages, $disabledChannels, $page, $pageSize);
        return $this->getArticles($ids, false, true);
    }

    public function getArticlesByUniqueIds(array $uniqueIds): array
    {
        return $this->getArticles($uniqueIds, false, true);
    }


    private function searchArticlePerQuery($parts, $langs, $disabledChannels, $page, $pageSize)
    {
        $client = ClientBuilder::create()->setHosts([config('elasticsearch.elastic_search_url')])->build();
        $results = [];
        $start = ($page - 1) * $pageSize;
        if ($page - 1 > 0) {
            $start =  $start - 20;
        }
        if (count($parts)) {
            $params = $this->buildEsSearchReq($parts, $disabledChannels, $langs, $start, $pageSize);
            try{
                $response = $client->search($params);
            }catch(BadRequest400Exception $e){
                //no data in index, return empty array
                return $results;
            }
            foreach ($response['hits']['hits'] as $obj) {
                $results[] = $obj['_source']['uniqueID'];
            }
        } else {
            //log_emergency('wired case, empty parts ');
        }
        return $results;
    }

    private function searchArticlePerQueryV2($parts, $q, $langs, $disabledChannels, $page, $pageSize)
    {
        $client = ClientBuilder::create()->setHosts([config('elasticsearch.elastic_search_url')])->build();
        $results = [];
        $start = ($page - 1) * $pageSize;
        if ($page - 1 > 0) {
            $start =  $start - 20;
        }
        $start = 0;
        $queryPageSize = 50;
        if (count($parts)) {
            $params = $this->buildEsSearchReqV2($q, $disabledChannels, $langs, $start, $queryPageSize);
            try{
                $response = $client->search($params);
            }catch(BadRequest400Exception $e){
                //no data in index, return empty array
                return $results;
            }

            foreach ($response['hits']['hits'] as $obj) {
                $results[] = [
                    'uniqueID' => $obj['_source']['uniqueID'],
                    'publishedDate' => $obj['_source']['publishedDate'],
                ];
                //$obj['_source']['uniqueID'];
            }

            $startIndex = ($page - 1) * 10;

            $results = collect($results)
            ->sortByDesc('publishedDate')
            ->splice($startIndex, 10)
            ->pluck('uniqueID')
            ->toArray();

        } else {
            //log_emergency('wired case, empty parts ');
        }
        return $results;
    }

    private function searchArticlePerQueryV3($parts, $q, $langs, $disabledChannels, $page, $pageSize, $sortPublishedDate)
    {
        $client = ClientBuilder::create()->setHosts([config('elasticsearch.elastic_search_url')])->build();
        $results = [];
        $start = ($page - 1) * $pageSize;
        if (count($parts)) {
            $params = $this->buildEsSearchReqV2($q, $disabledChannels, $langs, $start, $pageSize, $sortPublishedDate);
            try{
                $response = $client->search($params);
            }catch(BadRequest400Exception $e){
                //no data in index, return empty array
                return $results;
            }

            foreach ($response['hits']['hits'] as $obj) {
                $results[] = [
                    'uniqueID' => $obj['_source']['uniqueID'],
                    'publishedDate' => $obj['_source']['publishedDate'],
                ];
                //$obj['_source']['uniqueID'];
            }

            $results = collect($results)
            // ->sortByDesc('publishedDate')
            ->pluck('uniqueID')
            ->toArray();

        } else {
            //log_emergency('wired case, empty parts ');
        }
        return $results;
    }

    private function buildEsSearchReq($parts, $disabledChannels, $langs, $from, $size)
    {

        $q = "";
        $i = 0;
        foreach ($parts as $p) {
            $q = $q . "($p)";
            $i++;
            if ($i <= count($parts) - 1)
                $q = $q . " AND ";
        }
        $disabledChannels = \explode(',', rtrim($disabledChannels, ","));
        return [
            'index' => config('elasticsearch.es_articles_index'),
            'body' => [
                "from" => $from,
                "size" => $size,
                "_source" => [
                    "uniqueID",
                    "publishedDate",
                    "title",
                ],
                "sort" => [
                    [
                        "publishedDate" => "desc"
                    ],
                    "_score"
                ],
                "query" => [
                    "bool" => [
                        "must" => [
                            "simple_query_string" => [
                                "fields" => [
                                    "title", "html", "categoryName"
                                ],
                                "query" => "$q",
                                "auto_generate_synonyms_phrase_query" => true
                            ]
                        ],
                        "must_not" => [
                            "terms" => [
                                "channelId" => $disabledChannels
                            ]
                        ],
                        "filter" => [
                            "bool" => [
                                "must" => array(
                                    0 => [
                                        "terms" => [
                                            "language" => array_values($langs)
                                        ]
                                    ],
                                    1 => [
                                        "range" => [
                                            "publishedDate" => [
                                                "lt" => time() * 1000,
                                                "gt" => 0,
                                            ]
                                        ]
                                    ]
                                )
                            ]
                        ],
                    ]
                ]
            ]
        ];
    }


    private function buildEsSearchReqV2($query, $disabledChannels, $langs, $from, $size, $sortPublishedDate = false)
    {
        if(Auth::user()->isDashboard()){
            $langs[] = "DEL";
        }

        $disabledChannels = \explode(',', rtrim($disabledChannels, ","));

        $req = [
            'index' => config('elasticsearch.es_articles_index'),
            'body' => [
                "from" => $from,
                "size" => $size,
                "_source" => [
                    "uniqueID",
                    "publishedDate",
                    "title",
                    "score",
                    "views"
                ],
                "sort" => [
                    "_score",
                    ["publishedDate" => "desc"],
                    ["score" => "desc"]
                ],
                "query" => [
                    'function_score' => [
                        'query' =>
                        [
                            "bool" => [
                                "minimum_should_match" => 1,
                                "should" => [
                                    [
                                        "multi_match" => [
                                            "type" => "phrase",
                                            "fields" => [
                                                "title^3",
                                            ],
                                            "query" => "$query",
                                            "analyzer" => "standard",
                                            "boost" => 10
                                        ]
                                    ],
                                    [
                                        "multi_match" => [
                                            "type" => "cross_fields",
                                            "fields" => [
                                                "title^1.5",
                                            ],
                                            "query" => "$query",
                                            "operator" => "and",
                                            "boost" => 8
                                        ]
                                    ],
                                    [
                                        "multi_match" => [
                                            "type" => "cross_fields",
                                            "fields" => [
                                                "description",
                                            ],
                                            "query" => "$query",
                                            "operator" => "and",
                                            "boost" => 3
                                        ]
                                    ],
                                    [
                                        "multi_match" => [
                                            "fields" => [
                                                "title",
                                                ],
                                            "query" => "$query",
                                            "fuzziness" => 1,
                                            "operator" => "and"
                                            ],
                                    ],
                                ]
                            ]
                        ],
                    ],
                ]
            ]
        ];

        if ($sortPublishedDate == true){
            $req['body']['sort'] = [
                [
                    "publishedDate" => "desc"
                ],
                "_score",
            ];
        }

        if(!Auth::user()->isDashboard()){
            $req["body"]["query"]["function_score"]["query"]['bool']['must_not'] = [
                    "terms" => [
                        "channelId" => $disabledChannels
                    ]
            ];
            $req["body"]["query"]["function_score"]["functions"] = [
                [
                    "gauss" => [
                        "publishedDate" => [
                            "origin" => time() * 1000,
                            "scale"  => 86400000 * 7,
                            "offset" => 604800000,
                            "decay"  => 0.05
                        ]
                    ],
                    "weight" => 0.3
                ],
                [
                    "field_value_factor" => [
                        "field"    => "score",
                        "factor" => 2,
                        "missing"  => 1.0
                    ],
                    "weight" => 0.3
                ]

            ];
            $req["body"]["query"]["function_score"]["query"]['bool']['filter'] = [
                "bool" => [
                    "must" => array(
                        0 => [
                            "terms" => [
                                "language" => array_values($langs)
                            ]
                        ],
                        1 => [
                            "range" => [
                                "publishedDate" => [
                                    "lt" =>  time() * 1000,
                                    "gt" => 0,
                                ]
                            ]
                        ]
                    ),
                    "must_not" => array(
                        0 => [
                            "match" => [
                                "language" =>  "DEL"
                            ]
                        ]
                    )
                ]

            ];
        }else {
            $user_type = NewswavAuth::getUser()->request_type;
            if($user_type == "partner"){
                $prepo = app(PublisherRepository::class);
                $pub = collect(NewswavAuth::getPublishers())->toArray();
                if(!in_array(99999, $pub) && count($pub) > 0){
                    $channels = collect($prepo->getChannelsByPublisher($pub))->where('has_articles', 1)->pluck('id')->toArray();
                    $temp = $req['body']['query']['function_score']['query']['bool']['must'];
                    unset($req['body']['query']['function_score']['query']['bool']['must']);
                    $req['body']['query']['function_score']['query']['bool']['must'][] = $temp;
                    $req['body']['query']['function_score']['query']['bool']['must'][] = array (
                        'terms' => [
                            'channelId' => $channels
                        ]
                    );
                }
            }
        }

        return $req;
    }


    private function getSearchQuery($q)
    {
        $q = trim($q);
        $parts = explode(' ', $q);
        $verifiedParts = [];
        foreach ($parts as $part) {
            $part = trim($part);
            if (!$part || strlen($part) < 2)
                continue;
            $verifiedParts[] = $part;
        }

        if (count($verifiedParts) && !$this->isChinese($verifiedParts[count($verifiedParts) - 1])) {
            $verifiedParts[count($verifiedParts) - 1] = $verifiedParts[count($verifiedParts) - 1] . "*";
        }
        return $verifiedParts;
    }


    private function isChinese($word)
    {
        return preg_match("/\p{Han}+/u", $word);
    }


    public function getArticles(array $ids, $first = false, $idsOrder = false, $userTopics = [], $collection = false, $forceCompact = false, $pin = [])
    {
        /** @var FbUser|NwUser|NwWebUser|ModelsNwUser Auth::user() */
        $user = Auth::user();
        $articles = null;
        $article = null;
        $isDashboard = Auth::user()->isDashboard();
        if(count($ids) == 1 && $first && count($pin) == 0){
            $articleUniqueId = $ids[0];
            $windupArticle = nw_bunker('article', 'windup');

            $article = $this->getOldArticles([$articleUniqueId], true);
        } else {
            $pinnedVideos = collect();
            if (count($pin) > 0){
                $ids = array_unique(array_merge($pin, $ids));
                // https://newswav.atlassian.net/browse/NWGEN-1291?filter=10013
                // check pinned videos, fetch if exists
                $pVids = array_filter($pin, function ($item) {
                return like_match("V%_%", $item);
                });

                if (count($pVids) > 0){
                    // fetch videos
                    /** @var  VideoRepository $videoRepo */
                    $videoRepo = app(VideoRepository::class);
                    $pinnedVideos = $videoRepo->getVideosById($pVids, $user->getProfileId());
                }
            }

            $pollIds = array_filter($ids, function ($item) {
                return like_match("A%_P_%", $item);
            });
            $pollItems = collect();
            /** @var PollService $pollService */
            $pollService = app(PollService::class);
            if (count($pollIds) > 0 ){
                $pollItems = $pollService->getPollsBody($pollIds, $user->getFirebaseId(), false);
            }

            $articles = $this->getOldArticles($ids, $first, $idsOrder, $userTopics, $collection, $forceCompact);
        }
        $profileId =  $user->getProfileId() ;
        $firebase_id = $user->getFirebaseId();
        $userArticlesData = $this->getUserArticlesData($profileId, $ids);
        $userBookmarkData = $this->getUserBookmarkData($ids, $firebase_id);
        $userHistoryData = $this->getUserHistoryData($ids, $firebase_id);
        $disabledContents = $isDashboard ? $this->getDisabledContents($ids) : collect([]);
        if($article){
            $article->is_bookmark = "0";
            $article->is_history = "0";
            $article->userReactionType = $userArticlesData["reactions"]->get($article->uniqueID)->reaction_type ?? 0;
            $article->userReactionType  = $article->userReactionType ."";
            if(count($userBookmarkData) > 0){
                if (in_array($article->uniqueID, $userBookmarkData)){
                    $article->is_bookmark = "1";
                }
            }
            if(count($userHistoryData)>0){
                if (in_array($article->uniqueID, $userHistoryData)){
                    $article->is_history = "1";
                }
            }
            if ($isDashboard){
                //add this flag only when isDashboard is true
                //avoid returning this to client
                $d = $disabledContents->get($article->uniqueID);
                $article->canHide = $d ? (isPartner($d->hidden_by) ? true : false) : true;
                unset($d);
            }
        } else if ($articles){
            foreach($articles as $a){
                $a->is_bookmark = "0";
                $a->is_history = "0";
                $a->userReactionType = $userArticlesData["reactions"]->get($a->uniqueID)->reaction_type ?? 0;
                $a->userReactionType  = $a->userReactionType ."";
                if (in_array($a->uniqueID, $pin)){
                    $a->pinned = "1";
                }
                if(count($userBookmarkData)>0){
                    if (in_array($a->uniqueID, $userBookmarkData)){
                        $a->is_bookmark = "1";
                    }
                }
                if(count($userHistoryData)>0){
                    if (in_array($a->uniqueID, $userHistoryData)){
                        $a->is_history = "1";
                    }
                }
                if (like_match("A%_P_%", $a->uniqueID)){
                    $a->kind = "poll";
                    if ($pollItems->get($a->uniqueID)) {
                        $a->extra = $pollItems->get($a->uniqueID)->first();
                        $a->design->showViews = "0";
                    } else {
                        log_emergency('Cannot find this poll' . $a->uniqueID);
                    }
                }

                if ($isDashboard){
                    //add this flag only when isDashboard is true
                    //avoid returning this to client
                    $d = $disabledContents->get($a->uniqueID);
                    $a->canHide = $d ? (isPartner($d->hidden_by) ? true : false) : true;
                    unset($d);
                }
                $a = replaceMediaInOldArticle($a);
            }
        }

        if ($article) {
            $article = replaceMediaInOldArticle($article);
        }

        if ($articles) {
            $keyedArticles = collect($articles)->keyBy('uniqueID');
            $articles = [];
            $pinnedVideos = $pinnedVideos->keyBy('unique_id');
            $merged = array_unique(array_merge($pin, $ids));
            foreach($merged as $uniqueId) {
                if (($pinnedVideos->isNotEmpty()) && like_match("V%_%", $uniqueId)) {
                    $video = $pinnedVideos->get($uniqueId);
                    if (!$video){
                        Log::error("could not find $uniqueId");
                    } else {
                        $video->pinned = 1;
                        $articles[] = $video;
                    }
                } else {
                    $found = $keyedArticles->get($uniqueId);
                    if(!empty($found)) {
                        $articles[] = $found;
                    }
                }
            }
            // $articles = collect($articles)->map(function($article) {
            //     return replaceMediaInOldArticle($article);
            // })->toArray();
        }

        return  $article ?? $articles;
    }


    private function getArticlesWeb(array $ids, $first = false, $idsOrder = false, $userTopics = [], $collection = false, $forceCompact = false, $pin = [])
    {

        //same as getArticles but we'll expect less data for web use
        $articles = null;
        $article = null;
        if (count($pin) > 0){
            $ids = array_unique(array_merge($pin, $ids));
        }
        $articles = $this->getWebArticles($ids, $first, $idsOrder, $userTopics, $collection, $forceCompact);
        //$articles = $this->getWebArticles($ids, $first, $idsOrder, $userTopics, $collection, $forceCompact);

        //$profileId =  Auth::user()->getProfileId() ;
        //$userArticlesData = $this->getUserArticlesData($profileId, $ids);
        foreach($articles as $a){
            if (in_array($a->uniqueID, $pin)){
                $a->pinned = 1;
            }
        }
        return  $article ?? $articles;
    }

    private function getUserArticlesData($profileId, $articlesIds){
        /** @var PostRepository $postRepo  */
        $postRepo = app(PostRepository::class);
        $userReactions = $postRepo->getUserReactionsOnArticleEntities($articlesIds, $profileId);
        return ["reactions" => $userReactions];
    }

    private function getBriefArticles(array $ids, $first = false, $idsOrder = false, $userTopics = false)
    {

        $userTopicsArray = $userTopics  ? explode(',', $userTopics) : [];

        $query = DB::table('article')
            ->leftJoin('channels', 'channels.id', '=', "article.channelID")
            ->whereIn('uniqueID', $ids)
            ->select(
                'article.id',
                'article.uniqueID',
                'article.publishedDate',
                'article.url',
                'article.title',
                'article.media',
                'article.channelID',
                'article.viewCount',
                'article.commentCount',

                "channels.name as channelName",
                "channels.id as channelId",
                "channels.publisher_id as publisherId",

            );


        if ($idsOrder) {
            $idsString = "";
            foreach ($ids as $id) {
                $idsString = $idsString . "'$id',";
            }
            $idsString = trim($idsString, ',');
            $query->orderByRaw("FIELD (article.uniqueID, $idsString)");
        } else {
            $query->orderBy('article.publishedDate', 'DESC');
        }

        $articles = $query->get();

        $media = array();
        $publishers = array();
        $reactions = array();

        $articles->each(function ($item, $key) use (&$media, &$publishers) {
            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                if (count($medias) > 0) {
                    array_push($media, ...$medias);
                }
            }


            if ($item->publisherId &&  $item->publisherId > 0) {
                $publishers[] = $item->publisherId;
            }
        });

        $mediaData = false;
        $publishersData = false;



        if (count($media) > 0) {
            $mediaData = DB::table('media')
                ->select('id', 'caption', 'url', 'originalUrl')
                ->whereIn('id', $media)
                ->get()
                ->keyBy('id');
        }

        if (count($publishers) > 0) {
            $publishersData = DB::table('publishers')
                ->select(DB::raw('id,name,logo_url,true as brief'))
                ->whereIn('id', $publishers)
                ->get();
        }



        $reactionData = DB::table('articleReaction')
            ->select('happy', "cry", "shocked", "articleID", "angry", "meh", "laugh")
            ->whereIn('articleID', $ids)
            ->get()
            ->keyBy('articleID');

        $stats = DB::table('article_stats')
            ->select('shares', "views", "comments", "reactions", "article_id")
            ->whereIn('article_id', $ids)
            ->get()
            ->keyBy('article_id');


        $articles = $articles->map(function ($item, $key) use (
            &$mediaData,
            &$categoriesData,
            &$tagsData,
            &$reactionData,
            &$publishersData,
            &$topicsData,
            &$userTopicsArray,
            $stats
        ) {
            $item->mediaArray = array();




            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                $medias = array_unique($medias);
                foreach ($medias as $m) {
                    if ($m && strlen($m) > 0) {
                        $media = $mediaData->get($m);
                        $item->mediaArray[] = [
                            'id' => $media->id,
                            'url' => $media->url,
                            //'compressUrl' => $media->originalUrl
                        ];
                        break;
                    }
                }
            }



            $item->articleReaction = $reactionData->get($item->uniqueID);
            if (!$item->articleReaction) {
                $articleReaction = (object) [
                    'happy' => '0',
                    'cry' => '0',
                    'shocked' => '0',
                    'meh' => '0',
                    'angry' => '0',
                    'laugh' => '0'
                ];

                $item->articleReaction = $articleReaction;
            } else {
                unset($item->articleReaction->id);
                unset($item->articleReaction->updatedDate);
                unset($item->articleReaction->articleID);
            }

            $item->brief = true;
            if ($item->channelID) {
                $chId = $item->publisherId;
                $item->publisher =  $publishersData->first(function ($value, $key) use ($chId) {
                    return $value->id == $chId;
                });
            }

            $item->views = max($item->viewCount ?? 0, $stats->get($item->uniqueID)->views ?? 0);
            $item->comments = max($item->commentCount ?? 0, $stats->get($item->uniqueID)->comments ?? 0);
            $item->shares =  $stats->get($item->uniqueID)->shares ?? 0;
            unset($item->media);
            unset($item->channelID);
            unset($item->channelId);
            unset($item->channelName);
            unset($item->commentCount);
            unset($item->viewCount);



            // $topics = $topicsData->get($item->uniqueID)->toArray() ?? [];
            // if ($topics && !is_array($topics)) {
            //     $topics = [$topics];
            // }
            //var_dump();
            //$item->segmentTopic =  [];
            //$item->segmentTopic = $topics;
            // if (!count($userTopicsArray) && $topics) {
            //     $item->segmentTopic = [$topics[0]];
            // } else if (count($userTopicsArray)) {
            //     $item->segmentTopic = [$topics[0]];
            //     foreach ($topics as $t) {
            //         if (in_array($t->id, $userTopicsArray ?? [])) {
            //             $item->segmentTopic = [$t];
            //             break;
            //         }
            //     }
            // }
            // unset($item->segmentTopic[0]->unique_id);
            return $item;
        });

        if ($first && count($articles->toArray()) == 0) {
            abort(404, "article not found ".json_encode($ids));
        }

        // int_to_string_object($articles);

        return $first ? $articles->toArray()[0] : $articles->toArray();
    }


    private function getOldArticles(array $ids, $first = false, $idsOrder = false, $userTopics = [], $collection = false, $forceCompact = false)
    {
        $cdnUrl = nw_bunker('gcloud', 'cdn_newswav_url', 'https://cdn.newswav.com/');
        $hostUrl = nw_bunker('host', 'https', 'https://newswav.com/');

        $start = microtime(true);
        $query = DB::table('article')
            ->leftJoin('category', 'category.id', '=', 'article.categoryID')
            ->leftJoin('channels', 'channels.id', '=', "article.channelID")
            ->leftJoin('hide_ads_contents', 'hide_ads_contents.unique_id', '=', "article.uniqueID")
            ->leftJoin('predictions', 'predictions.unique_id', '=', 'article.uniqueID')
            ->whereIn('uniqueID', $ids)
            ->select(
                'article.id',
                'article.uniqueID',
                'article.articleID',
                'article.updatedDate',
                'article.createdDate',
                'article.publishedDate',
                'article.modifiedDate',
                'article.channelID',
                'article.categoryID',
                'article.categoryIDArray',
                'article.url',
                'article.canonicalURL',
                'article.title',
                'article.author',
                'article.description',
                'article.media',
                'article.tags',
                'article.tagsName',
                'article.html',
                'article.relatedArticle',
                'article.keywordDescription',
                'article.keywordHTML',
                'article.segmentTopicIds',
                'article.isOnline',
                'article.isFixed',
                'article.permalink',
                'predictions.topic',
                'category.name as categoryName',
                'category.id as categoryId',
                "channels.name as channelName",
                "channels.image_url as channelImageURL",
                "channels.image_night_url as channelImageNightURL",
                "channels.language",
                "channels.ad_enabled as adEnable",
                "channels.reader_view_only as readerViewOnly",
                "channels.show_website as showWebsite",
                "channels.js_enabled as enableJS",
                "channels.boost_reader_view as boostReaderView",
                "hide_ads_contents.unique_id as hideAds",
                "hide_ads_contents.hidden_at as adsHiddenAt"
            );

        if ($idsOrder) {
            $idsString = "";
            foreach ($ids as $id) {
                $idsString = $idsString . "'$id',";
            }
            $idsString = $idsString . "'0'";

            $query->orderByRaw("FIELD (article.uniqueID, $idsString)");
        } else {
            $query->orderBy('article.publishedDate', 'DESC');
        }

        $articles = $query->get();

        $categories = array();
        $media = array();
        $tags = array();
        $publishers = array();
        $reactions = array();

        $articles->each(function ($item, $key) use (&$media, &$categories, &$tags, &$publishers, $cdnUrl, $hostUrl) {

            if (in_array($item->uniqueID, nw_bunker('article', 'malaysiakini_covid'))) {
                $item->author = "MalaysiaKini";
                $item->channelID = "30";
                $item->publishedDate = Carbon::now()->toDateTimeString();
                $item->channelId = "30";
                $item->adEnable = "0";
                $item->readerViewOnly = "0";
                $item->showWebsite = "1";
                $item->enableJS = "1";
                $item->channelName = "MalaysiaKini";
                $item->language = "en";
                $item->channelImageNightURL = $cdnUrl . "nonredirect/publisherImage/MalaysiaKini.jpg";
                $item->channelImageURL = $cdnUrl . "nonredirect/publisherImage/MalaysiaKini.jpg";
            }

            if(in_array($item->uniqueID, nw_bunker('article', 'windup'))){
                $firebaseId = Auth::user()->getFirebaseId() ?? "XXXX";
                $item->url = $hostUrl . "windup/2020/$firebaseId?utm_source=Newswav&utm_medium=App";
                $item->canonicalURL = $hostUrl . "windup/2020/$firebaseId?utm_source=Newswav&utm_medium=App";
            }

            if(in_array($item->uniqueID, nw_bunker('article', 'olympic'))){
                $item->url = "https://olympics.com/tokyo-2020/paralympic-games/en/results/all-sports/medal-standings.htm?utm_source=Newswav&utm_medium=App&time=".time();
                $item->canonicalURL = "https://olympics.com/tokyo-2020/paralympic-games/en/results/all-sports/medal-standings.htm?utm_source=Newswav&utm_medium=App&time=".time();
            }

            if(in_array($item->uniqueID, nw_bunker('article', 'sabah_election_2020'))){
                $item->url = $item->url."&r=".rand(0,99999);
                $item->canonicalURL = $item->canonicalURL."&r=".rand(0,99999);
            }

            if (in_array($item->uniqueID, nw_bunker('article', 'covid'))) {
                $item->url = $item->url . "&xy" . rand(0, 999) . "=" . rand(0, 999) . "&z=" . rand(0, 99);
            }

            if ($item->categoryIDArray && $item->categoryIDArray != '') {
                $cats = explode('|', $item->categoryIDArray);
                if (count($cats) > 0) {
                    array_push($categories, ...$cats);
                }
            }

            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                if (count($medias) > 0) {
                    array_push($media, ...$medias);
                }
            }

            if ($item->tags && $item->tags != '') {
                $t = explode(',', $item->tags);
                if (count($t) > 0) {
                    array_push($tags, ...$t);
                }
            }

            if ($item->channelID &&  $item->channelID > 0) {
                $publishers[] = $item->channelID;
            }

            // echo "$item->id -> $key -\n";
        });

        $categoriesData = false;
        $mediaData = false;
        $tagsData = false;
        $publishersData = false;

        /*
        // Don't fill
        if (count($categories) > 0) {
            $categoriesData = DB::table('category')
                ->whereIn('id', $categories)
                ->get()
                ->keyBy('id');
        }
         */
        $topicsRepo = app(TopicRepository::class);
        $topicsData =    $topicsRepo->getArticleTopics($ids);

        $deletedData = DB::table('predictions')
        ->select(DB::raw("unique_id, IF(language = 'DEL', 1, 0) as deleted") )
        ->whereIn('unique_id', $ids)
        ->get()
        ->keyBy('unique_id');

        $disabledCommentsData = DB::table('disabled_comments')
        ->whereIn('unique_ID', $ids)
        ->whereNull('deleted_at')
        ->get()
        ->keyBy('unique_ID');

        $boostData = getBoostData()->keyBy('unique_id');

        if (count($media) > 0) {
            $mediaData = DB::table('media')
                ->leftJoin('thumbnail', function($join) {
                    $join->on('media.id','=','thumbnail.mediaOwnerId')->where('thumbnail.mediaOwnerType', '=', 'media');
                })
                ->whereIn('media.id', $media)
                ->selectRaw(
                    // use empty string as fallback in this case. iOS will crash if response contains null
                    DB::raw(
                        "media.*, ifnull(thumbnail.squareUrl, media.url) as squareUrl, ifnull(thumbnail.wideUrl, media.url) as wideUrl"
                    )
                )
                ->get()
                ->keyBy('id');
        }

        if (count($tags) > 0) {
            $tagsData = DB::table('tag')
                ->whereIn('id', $tags)
                ->get()
                ->keyBy('id');
        }

        if (count($publishers) > 0) {
            $publisherRepo = app(PublisherRepository::class);
            $publishersData =  $publisherRepo->getPublishersByChannelIds($publishers);
        }

        $reactionData = DB::table('articleReaction')
            ->whereIn('articleID', $ids)
            ->get()
            ->keyBy('articleID');

        $statsData = DB::table('article_stats')
            ->whereIn('article_id', $ids)
            ->select(
                'article_id',
                'shares as shares',
                'reactions as reactionCount',
                'comments as commentCount',
                'views as viewCount',
            )
            ->get()
            ->keyBy('article_id');

        $articles = $articles
        ->map(function ($item) use (
            $mediaData,
            $tagsData,
            $reactionData,
            $publishersData,
            $topicsData,
            $forceCompact,
            $boostData,
            $statsData,
            $deletedData,
            $disabledCommentsData,
            $hostUrl,
            $cdnUrl
        ) {
            // hide ads flag
            $item->hideAds = $item->hideAds && $item->adsHiddenAt ? true : false;
            unset($item->adsHiddenAt);
            $item->mediaArray = array();
            $item->recommendedArticleArray = array();
            $item->tagsArray = array();

            unset($item->keywordTitle);
            unset($item->keywordDescription);
            unset($item->keywordHTML);

            $item->html = str_replace('="//', '="http://', $item->html);
            $item->deleted =  $deletedData->get($item->uniqueID)->deleted ?? "0";
            $item->comments_disabled = $disabledCommentsData->get($item->uniqueID) ? "1" : "0";
            if ($item->tagsName != '') {
                $item->keywordArray = array_values(array_filter(explode(",", $item->tagsName)));
            } else {
                $item->keywordArray = array();
            }

            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                $isSquare = count($medias) > 1; // required by app to display in carousel (square)

                foreach ($medias as $m) {
                    if ($m && strlen($m) > 0) {
                        $ob = $mediaData->get($m);
                        if ($ob){
                            //add thumbnail url here
                            $ob->thumbnail = $isSquare ? $ob->squareUrl : $ob->wideUrl;

                            $ob->aspectRatio = $ob->imageWidth > 0 && $ob->imageHeight > 0 ?
                                calculateAspectRatio($ob->imageWidth, $ob->imageHeight) :
                                '';

                            // fallback using original url if is empty
                            if (empty($ob->thumbnail)) {
                                $ob->thumbnail = $ob->url;
                            }

                            if(empty( $ob->thumbnail )){
                                $ob->thumbnail = $ob->url;
                            }
                            $item->mediaArray[] = $ob;
                        }
                    }
                }
            }

            if ($item->tags && $item->tags != '') {
                $ts = explode(',', $item->tags);
                foreach ($ts as $t) {
                    $ob = $tagsData->get($t);
                    if ($ob)
                        $item->tagsArray[] = $ob->name;
                }
            }

            $stats = $statsData->get($item->uniqueID);
            if (!$stats){
                $item->commentCount = "0";
                $item->viewCount = "1";
                $item->shares = "0";
            }else{
                $item->commentCount = $stats->commentCount ? $stats->commentCount : "0";
                $item->viewCount = $stats->viewCount &&  $stats->viewCount > 0 ? $stats->viewCount : "1";
                $item->shares = $stats->shares ? $stats->shares : "0";
            }


            $item->articleReaction = $reactionData->get($item->uniqueID);
            if (!$item->articleReaction) {
                $articleReaction = (object) [
                    'articleID' => $item->uniqueID,
                    'happy' => '0',
                    'cry' => '0',
                    'shocked' => '0',
                    'meh' => '0',
                    'angry' => '0',
                    'laugh' => '0'
                ];

                $item->articleReaction = $articleReaction;
                $item->reactionCount = "0";
            } else {
                $item->reactionCount = (int)$item->articleReaction->happy +
                (int)$item->articleReaction->cry +
                (int)$item->articleReaction->shocked +
                (int)$item->articleReaction->angry +
                (int)$item->articleReaction->laugh +
                (int)$item->articleReaction->meh;
            }

            if ($item->channelID) {
                $chId = $item->channelID;
                $item->publisher =  $publishersData->first(function ($value, $key) use ($chId) {
                    return in_array($chId, $value->channels);
                });
                if(!$item->publisher){
                   log_emergency('Publisher not found ! for channel '.$chId);
                   //$item->publisher->canOpen = "0";
                } else {
                    $item->publisher->canOpen =  $item->publisher->id == 369 ? "0"  :  "1";
                }
            }

            //will ignore now
            $item->categoryIDArray = array();
            $item->categoryArray = array();
            if ($item->categoryId) {
                $item->categoryIDArray[] = $item->categoryId;
            } else {
                $item->categoryId = "0";
                $item->categoryIDArray[] = "0";
            }

            if ($item->categoryName) {
                $item->categoryArray[] = $item->categoryName;
            } else {
                $item->categoryName =  $item->channelName;
                $item->categoryArray[] =  $item->channelName;
            }
            $item->segmentTopic =  $topicsData->get($item->uniqueID) ? [$topicsData->get($item->uniqueID)->first()] : [];
            $dataToShow = collect([
                ['name' => 'showReactions', 'count' => 5 * $item->reactionCount],
                ['name' => 'showComments', 'count' => 10 * $item->commentCount],
                ['name' => 'showViews', 'count' => $item->viewCount]
            ])->sortByDesc('count')
                ->whereNotIn('count', [0])
                ->slice(0, 3)
                ->keyBy('name');

            if (Str::startsWith($item->url, "https://capital-stock.com/")) {
                $item->url = $hostUrl . $item->uniqueID;
            }

            if (Str::startsWith($item->canonicalURL, "https://capital-stock.com/")) {
                $item->canonicalURL = $hostUrl . $item->uniqueID;
            }

            $isPoll = like_match('A%_P_%', $item->uniqueID);
            $item->design = (object) [
                "type" => 0,
                "compact" => $forceCompact ? 1 : 0,
                "version" => $this->designVersion,
                "showReactions" => $dataToShow->get('showReactions') ? 1 : 0,
                "showViews" => $isPoll ? 0 : ($dataToShow->get('showViews') ? 1 : 0),
                "showShares" => $dataToShow->get('showShares') ? 1 : 0,
                "showComments" => $dataToShow->get('showComments') ? 1 : 0
            ];

            if($boostData->get($item->uniqueID)){
                $item->articleReaction->happy = $item->articleReaction->happy  + $boostData->get($item->uniqueID)->happy;
                $item->articleReaction->shocked = $item->articleReaction->shocked  + $boostData->get($item->uniqueID)->shocked;
                $item->articleReaction->laugh = $item->articleReaction->laugh  + $boostData->get($item->uniqueID)->laugh;
                $item->viewCount = $item->viewCount  + $boostData->get($item->uniqueID)->views;
                $item->reactionCount = $item->reactionCount  + $boostData->get($item->uniqueID)->laugh+ $boostData->get($item->uniqueID)->shocked+ $boostData->get($item->uniqueID)->happy;
            }

            $articleIDSabahElection = nw_bunker('article', 'sabah_election_2020');

            if(in_array($item->uniqueID, $articleIDSabahElection)) {
                $item->url = $cdnUrl . "sabah-election/sabah-area-candidate.html?r=" . rand(0,999) . "&x=" . rand(0,9999);
                $item->canonicalURL = $cdnUrl . "sabah-election/sabah-area-candidate.html?r=".rand(0,999)."&x=".rand(0,9990);
            }

            return $item;
        });
        if(!Auth::user()->isDashboard()){
            $articles = $articles->where('deleted', "0");
        }


        if ($first && count($articles->toArray()) == 0) {
            abort(404, "article not found ".json_encode($ids));
        }

        int_to_string_object($articles);

        $end = microtime(true);

        if ($collection) return $articles;
        $d = $end - $start;
        return $first ? $articles->toArray()[0] : array_values($articles->all());
    }


    private function getWebArticles(array $ids, $first = false, $idsOrder = false, $userTopics = [], $collection = false, $forceCompact = false)
    {
        $cdnUrl = nw_bunker('gcloud', 'cdn_newswav_url', 'https://cdn.newswav.com/');
        $hostUrl = nw_bunker('host', 'https', 'https://newswav.com/');

        $start = microtime(true);
        $query = DB::table('article')
            ->leftJoin('category', 'category.id', '=', 'article.categoryID')
            ->leftJoin('channels', 'channels.id', '=', "article.channelID")
            ->whereIn('uniqueID', $ids)
            ->select(
                'article.id',
                'article.uniqueID',
                'article.articleID',
                'article.updatedDate',
                'article.createdDate',
                'article.publishedDate',
                'article.channelID',
                'article.url',
                'article.canonicalURL',
                'article.title',
                'article.author',
                'article.description',
                'article.media',
                "channels.name as channelName",
                "channels.image_url as channelImageURL",
                "channels.image_night_url as channelImageNightURL",
            );



        if ($idsOrder) {
            $idsString = "";
            foreach ($ids as $id) {
                $idsString = $idsString . "'$id',";
            }
            $idsString = $idsString . "'0'";

            $query->orderByRaw("FIELD (article.uniqueID, $idsString)");
        } else {
            $query->orderBy('article.publishedDate', 'DESC');
        }

        $articles = $query->get();

        $media = array();
        $publishers = array();

        $now = new \DateTime;

        $articles->each(function ($item, $key) use (&$media, &$categories, &$tags, &$publishers, $now, $cdnUrl, $hostUrl) {

            $item->updatedDate = $this->time_elapsed_string($item->updatedDate, $now);
            $item->createdDate = $this->time_elapsed_string($item->createdDate, $now);
            $item->timeAgo = $this->time_elapsed_string($item->publishedDate, $now);

            if (in_array($item->uniqueID, nw_bunker('article', 'malaysiakini_covid'))) {
                $item->author = "MalaysiaKini";
                $item->channelID = "30";
                $item->publishedDate = Carbon::now()->toDateTimeString();
                $item->channelId = "30";
                $item->adEnable = "0";
                $item->readerViewOnly = "0";
                $item->showWebsite = "1";
                $item->enableJS = "1";
                $item->channelName = "MalaysiaKini";
                $item->language = "en";
                $item->channelImageNightURL = $cdnUrl . "nonredirect/publisherImage/MalaysiaKini.jpg";
                $item->channelImageURL = $cdnUrl . "nonredirect/publisherImage/MalaysiaKini.jpg";
            }

            if(in_array($item->uniqueID, nw_bunker('article', 'windup'))){
                $firebaseId = Auth::user()->getFirebaseId() ?? "XXXX";
                $item->url = $hostUrl . "windup/2020/$firebaseId?utm_source=Newswav&utm_medium=App";
                $item->canonicalURL = $hostUrl . "windup/2020/$firebaseId?utm_source=Newswav&utm_medium=App";
            }

            if(in_array($item->uniqueID, nw_bunker('article', 'sabah_election_2020'))){
                $item->url = $item->url."&r=".rand(0,99999);
                $item->canonicalURL = $item->canonicalURL."&r=".rand(0,99999);
            }

            if (in_array($item->uniqueID, nw_bunker('article', 'covid'))) {
                $item->url = $item->url . "&xy" . rand(0, 999) . "=" . rand(0, 999) . "&z=" . rand(0, 99);
            }


            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                if (count($medias) > 0) {
                    array_push($media, ...$medias);
                }
            }


            if ($item->channelID &&  $item->channelID > 0) {

                $publishers[] = $item->channelID;
            }


            // echo "$item->id -> $key -\n";
        });

        $mediaData = false;
        $publishersData = false;
        /*
        // Don't fill
        if (count($categories) > 0) {
            $categoriesData = DB::table('category')
                ->whereIn('id', $categories)
                ->get()
                ->keyBy('id');
        }
         */

        $deletedData = DB::table('predictions')
        ->select(DB::raw("unique_id, IF(language = 'DEL', 1, 0) as deleted") )
        ->whereIn('unique_id', $ids)
        ->get()
        ->keyBy('unique_id');

        $boostData = getBoostData()->keyBy('unique_id');

        if (count($media) > 0) {
            $mediaData = DB::table('media')
                ->whereIn('id', $media)
                ->get()
                ->keyBy('id');
        }
        if (count($publishers) > 0) {
            $publisherRepo = app(PublisherRepository::class);
            $publishersData =  $publisherRepo->getPublishersByChannelIds($publishers);
        }

        $reactionData = DB::table('articleReaction')
            ->whereIn('articleID', $ids)
            ->get()
            ->keyBy('articleID');

        $statsData = DB::table('article_stats')
            ->whereIn('article_id', $ids)
            ->select(
                'article_id',
                'shares as shares',
                'reactions as reactionCount',
                'comments as commentCount',
                'views as viewCount',
            )
            ->get()
            ->keyBy('article_id');

        $articles = $articles
        ->map(function ($item, $key) use (
            $mediaData,
            $reactionData,
            $publishersData,
            $boostData,
            $statsData,
            $deletedData,
            $cdnUrl,
            $hostUrl
        ) {
            $item->mediaArray = array();


            $item->deleted =  $deletedData->get($item->uniqueID)->deleted ?? "0";


            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                    // if ($item->id == 3747667){

                    // }
                    $ob = $mediaData->where('id',$medias[0])->filter(function ($item) {
                        return (!strpos($item->url, ".gif"));
                    })->first();
                    if ($ob)
                        //use imageproxy to fix https problem
                        $ob->url = imageProxy($this->imageproxykey, $ob->url);
                        $item->mediaArray[] = $ob;
            }


            $stats = $statsData->get($item->uniqueID);
            if (!$stats){
                $item->commentCount = "0";
                $item->viewCount = "1";
                $item->shares = "0";
            }else{
                $item->commentCount = $this->humanize_number($stats->commentCount ? (int)$stats->commentCount : 0);
                $item->viewCount = $this->humanize_number($stats->viewCount &&  (int)$stats->viewCount > 0 ? $stats->viewCount : 1);
                $item->shares = $this->humanize_number($stats->shares ? (int)$stats->shares : 0);
            }


            $item->articleReaction = $reactionData->get($item->uniqueID);
            if (!$item->articleReaction) {
                $articleReaction = (object) [
                    'articleID' => $item->uniqueID,
                    'happy' => '0',
                    'cry' => '0',
                    'shocked' => '0',
                    'meh' => '0',
                    'angry' => '0',
                    'laugh' => '0'
                ];

                $item->articleReaction = $articleReaction;
                $item->reactionCount = $this->humanize_number(0);
            } else {
                $item->reactionCount = $this->humanize_number((int)$item->articleReaction->happy +
                (int)$item->articleReaction->cry +
                (int)$item->articleReaction->shocked +
                (int)$item->articleReaction->angry +
                (int)$item->articleReaction->laugh +
                (int)$item->articleReaction->meh);


            }
            $item->articleReaction2 = $this->get_top_reaction($item->articleReaction);

            if ($item->channelID) {
                $chId = $item->channelID;
                $item->publisher =  $publishersData->first(function ($value, $key) use ($chId) {
                    return in_array($chId, $value->channels);
                });
                if(!$item->publisher){
                   log_emergency('Publisher not found ! for channel '.$chId);
                   //$item->publisher->canOpen = "0";
                } else {
                    $item->publisher->canOpen =  $item->publisher->id == 369 ? "0"  :  "1";
                }
            }

            if (Str::startsWith($item->url, "https://capital-stock.com/")) {
                $item->url = $hostUrl . $item->uniqueID;
            }

            if (Str::startsWith($item->canonicalURL, "https://capital-stock.com/")) {
                $item->canonicalURL = $hostUrl . $item->uniqueID;
            }


            if($boostData->get($item->uniqueID)){
                $item->articleReaction->happy = $item->articleReaction->happy  + $boostData->get($item->uniqueID)->happy;
                $item->articleReaction->shocked = $item->articleReaction->shocked  + $boostData->get($item->uniqueID)->shocked;
                $item->articleReaction->laugh = $item->articleReaction->laugh  + $boostData->get($item->uniqueID)->laugh;
                $item->viewCount = $item->viewCount  + $boostData->get($item->uniqueID)->views;
                $item->reactionCount = $item->reactionCount  + $boostData->get($item->uniqueID)->laugh+ $boostData->get($item->uniqueID)->shocked+ $boostData->get($item->uniqueID)->happy;
            }

            $articleIDSabahElection = nw_bunker('article', 'sabah_election_2020');

            if(in_array($item->uniqueID, $articleIDSabahElection)){
                $item->url = $cdnUrl . "sabah-election/sabah-area-candidate.html?r=".rand(0,999)."&x=".rand(0,9999);
                $item->canonicalURL = $cdnUrl . "sabah-election/sabah-area-candidate.html?r=".rand(0,999)."&x=".rand(0,9990);
            }

            return $item;
        });
        if(!Auth::user()->isDashboard()){
            $articles = $articles->where('deleted', "0");
        }


        if ($first && count($articles->toArray()) == 0) {
            abort(404, "article not found ".json_encode($ids));
        }

        int_to_string_object($articles);

        $end = microtime(true);

        if ($collection) return $articles;
        $d = $end - $start;
        return $first ? $articles->toArray()[0] : array_values($articles->all());
    }

    public function getForYouArticlesV2(
        string $sessionId,
        ?int $sessionTime,
        array $languages,
        ?array $topics
    ): array {
        $newSession = false;
        if (!$sessionTime || $sessionTime < (time() - 3600) || $sessionTime > time()) {
            $sessionTime = time();
            DB::table('user_foryou_session_feed')
                ->where('pid', $sessionId)
                ->where('session_time', '<', $sessionTime)
                ->delete();
            $newSession = true;
        }

        $feedString = null;
        if (Str::startsWith($sessionId, "i_") || Str::startsWith($sessionId, "a_")) {
            $feed = cache_feed_get($sessionId);
            if (count($feed)) {
                $feedString = implode("','", $feed);
                $feedString = "'$feedString'";
            }
        }
        //get articles ids from pools
        if ($topics) { // personalaized
            $schema = "NHTHNTEHNT";
            $pageSize = strlen($schema);
            $selectedArticles = $this->getPersonlaizedForyouArticles($schema, $topics, $sessionId, $sessionTime, $languages, $feedString);
        } else { //not personalaized
            $pageSize = 10;
            $selectedArticles = $this->getPublicForyouArticles($sessionId, $sessionTime, $languages, $pageSize,  $feedString);
        }

        //----------
        //save served feed in db to ignore in paginations
        foreach ($selectedArticles as $k => $v) {
            $data[] = [
                'article_id' => $k,
                'session_time' => $sessionTime,
                'pid' => $sessionId,
            ];
        }
        //Log::info($selectedArticles);
        $this->saveSessionFeed($data);
        //----------------
        $articles = $this->getArticles(array_keys($selectedArticles), false, true);
        return [
            "sessionId" => $sessionId,
            "newSession" => $newSession,
            "sessionTime" => $sessionTime,
            "more" => count($selectedArticles) == $pageSize,
            "articles" => $articles
        ];
    }

    private function saveSessionFeed($rows): void
    {
        $values = '';
        foreach ($rows as $row) {
            $values .= "('{$row['pid']}','{$row['article_id']}','{$row['session_time']}'),";
        }
        $values = trim($values, ',');
        $q = "INSERT into `user_foryou_session_feed` (pid,article_id,session_time) values  $values";
        DB::statement($q);
    }

    //sessionId migh be profileId in most cases
    private function getMoreHotArticles($sessionId, $sessionTime, $languages, $topics = [], $reverse = false, $feedString = null, $muteChannels = [])
    {
        $langs = "'" . implode("','", $languages) . "'";
        $muteChannelsString = implode(',', $muteChannels);
        $muteChannelsClause = count($muteChannels) ?  "( channel not in ($muteChannelsString) )" : "(true)";
        $feedStringClause = $feedString ? " x.articleId NOT IN ($feedString) " : " 1 ";
        $reportedArticlesClause = '""';
        $firebaseId = Auth::user()->getFirebaseId();
        if($firebaseId){
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if($reportedArticles && count($reportedArticles)){
                foreach($reportedArticles as $article){
                    $reportedArticlesClause .= ',"'.$article.'"';
                }
            }
        }
        $reportedArticlesClause = "x.articleId not in ($reportedArticlesClause)";

        $topicsWhereClause =
            count($topics) > 0 ? ($reverse ?
                "AND topic not in (" . implode(",", $topics) . ")"
                : "AND topic in (" . implode(",", $topics) . ")")
            : " AND  1";
        $q = "SELECT q.*  FROM
            (
                SELECT x.articleId,views,published_date,ctr,score,pool_type,is_hot,topic
                FROM `new_foryou_articles_pool` as x
                WHERE language in ($langs) and
                disabled = 0 and
                pool_type = 'n'
                $topicsWhereClause and
                $feedStringClause and
                $muteChannelsClause and
                $reportedArticlesClause and
                NOT EXISTS
                (
                    SELECT 1 from user_foryou_session_feed as y
                    where y.pid = :pid1 and
                    y.session_time = :session_time and
                    y.article_id = x.articleId
                )
                order by is_hot desc,published_date desc LIMIT 400
            ) as q
        ";
        $articlesPool = collect(DB::select($q, ['pid1' => $sessionId, 'session_time' => $sessionTime]));
        $results = $this->mergeArticlesWithUserArticleStats($articlesPool, $sessionId)
            ->sortByMulti([
                'userViews' => 'ACK',
                'userFeed' => 'ACK',
                'is_hot' => 'DESC',
                'published_date' => 'DESC'
            ]);
        return $results;
    }

    private function getNewArticles($sessionId, $sessionTime, $languages, $topics = [], $feedString = null, $muteChannels = [])
    {
        $muteChannelsString = implode(',', $muteChannels);
        $muteChannelsClause = count($muteChannels) ?  "( channel not in ($muteChannelsString) )" : "(true)";
        $topicsString = implode(",", $topics);
        $langs = "'" . implode("','", $languages) . "'";
        $feedStringClause = $feedString ? " x.articleId NOT IN ($feedString) " : " 1 ";
        $topicsWhereClause = count($topics) > 0 ? "AND topic in ( $topicsString )" : " AND  1";

        $reportedArticlesClause = '""';
        $firebaseId = Auth::user()->getFirebaseId();
        if($firebaseId){
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if($reportedArticles && count($reportedArticles)){
                foreach($reportedArticles as $article){
                    $reportedArticlesClause .= ',"'.$article.'"';
                }
            }
        }
        $reportedArticlesClause = "x.articleId not in ($reportedArticlesClause)";


        $q = "SELECT q.* FROM
            (
                SELECT x.articleId,views,published_date,ctr,score,pool_type,is_hot,topic
                FROM `new_foryou_articles_pool` as x
                WHERE language in ($langs) and
                disabled = 0
                $topicsWhereClause and
                pool_type = 'n' and
                is_hot = 0 and
                $feedStringClause and
                $muteChannelsClause and
                $reportedArticlesClause and
                NOT EXISTS
                (
                    select 1 from user_foryou_session_feed as y
                    where y.pid = :pid1 and
                    y.session_time = :session_time and
                    y.article_id = x.articleId
                )
                order by FIELD(topic, $topicsString ), updated_at asc LIMIT 100
            ) as q
            order by  FIELD(topic, $topicsString ), published_date desc
        ";
        $articlesPool = collect(DB::select($q, ['pid1' => $sessionId, 'session_time' => $sessionTime]));
        $results = $this->mergeArticlesWithUserArticleStats($articlesPool, $sessionId)
            ->sortByMulti([
                'userFeed' => 'ACK',
                'userViews' => 'ACK',
                'published_date' => 'DESC'
            ]);
        return $results;
    }

    private function getTrendingArticles($sessionId, $sessionTime, $languages, $topics = [],  $feedString = null, $muteChannels = [])
    {
        $muteChannelsString = implode(',', $muteChannels);
        $muteChannelsClause = count($muteChannels) ?  "( channel not in ($muteChannelsString) )" : "(true)";
        $langs = "'" . implode("','", $languages) . "'";
        $feedStringClause = $feedString ? " x.articleId NOT IN ($feedString) " : " 1 ";
        $topicsWhereClause = count($topics) > 0 ? "AND topic in (" . implode(",", $topics) . ")" : " AND  1";

        $reportedArticlesClause = '""';
        $firebaseId = Auth::user()->getFirebaseId();
        if($firebaseId){
            $reportRepo = app(ReportRepository::class);
            $reportedArticles = $reportRepo->getUserReportedEntities($firebaseId);
            if($reportedArticles && count($reportedArticles)){
                foreach($reportedArticles as $article){
                    $reportedArticlesClause .= ',"'.$article.'"';
                }
            }
        }

        $reportedArticlesClause = "x.articleId not in ($reportedArticlesClause)";

        $q = "SELECT q.* FROM
            (
                SELECT x.articleId,views,published_date,ctr,score,pool_type,is_hot,topic,shares
                FROM `new_foryou_articles_pool` as x
                WHERE language in ($langs) and
                disabled = 0
                $topicsWhereClause and
                pool_type = 't' and is_hot = 0 and
                $feedStringClause and
                $muteChannelsClause and
                $reportedArticlesClause and
                NOT EXISTS
                (
                    select 1 from user_foryou_session_feed as y
                    where y.pid = :pid1 and
                    y.session_time = :session_time and
                    y.article_id = x.articleId
                )
                order by shares desc, published_date asc LIMIT 50
            ) as q
            order by  shares desc, published_date asc
        ";
        $articlesPool = collect(DB::select($q, ['pid1' => $sessionId, 'session_time' => $sessionTime]));
        $results = $this->mergeArticlesWithUserArticleStats($articlesPool, $sessionId)
            ->sortByMulti([
                'userFeed' => 'ACK',
                'userViews' => 'ACK',
                'shares' => 'DESC',
                'published_date' => 'DESC'
            ]);
        return $results;
    }

    private function getPersonlaizedForyouArticles($schema, $userTopics, $sessionId, $sessionTime, $languages,  $feedString = null): ?array
    {
        $selectedArticles = [];
        $pageSize = strlen($schema);

        $service = app(UserProfileService::class);
        $muteChannels = $service->getMutedChannels();

        $allHotArticles = $this->getMoreHotArticles($sessionId, $sessionTime, $languages, [], false, $feedString, $muteChannels);
        $newArticles = $this->getNewArticles($sessionId, $sessionTime, $languages, $userTopics, $feedString, $muteChannels);
        $trendingArticles = $this->getTrendingArticles($sessionId, $sessionTime, $languages, $userTopics, $feedString, $muteChannels);
        $len = 0;
        foreach (str_split($schema) as $char) {
            $forceMode = false;
            $len++;
            $rounds = 0;
            while (count($selectedArticles) < $len && $rounds < 2) {
                $articles = false;
                switch ($char) {
                    case 'H':
                        $articles = $allHotArticles;
                        break;
                    case 'N':
                        $articles = $newArticles;
                        break;
                    case 'T':
                        $articles = $trendingArticles;
                        break;
                    case 'E':
                        $articles = $allHotArticles;
                        break;
                    default:
                        throw new \Exception('Unexpexted char ' . $char);
                }
                foreach ($articles as $article) {
                    if ($selectedArticles[$article->articleId] ?? 0) continue;
                    if ($char === "H" && !in_array($article->topic, $userTopics)) continue;
                    if ($char === "E" && in_array($article->topic, $userTopics)) continue;
                    if ((!$article->userFeed && !$article->userViews) || $forceMode) {
                        $selectedArticles[$article->articleId] = "{$char}_{$article->topic}"; //could be score
                        break 2;
                    }
                    if (count($selectedArticles) >= $pageSize) break 2;
                }
                // $allHotArticles = $allHotArticles->shuffle();
                $newArticles = $newArticles->shuffle();
                // $trendingArticles = $trendingArticles->shuffle();
                $forceMode = true;
                $rounds++;
            }
            if (count($selectedArticles) >= $pageSize) break;
        }
        //fill unfilled articles
        if (count($selectedArticles) < $pageSize) {
            $allHotArticles = $allHotArticles->shuffle();
            foreach ($allHotArticles as $article) {
                if ($selectedArticles[$article->articleId] ?? 0) continue;
                $selectedArticles[$article->articleId] = "HF_{$article->topic}"; //could be score
                if (count($selectedArticles) >= $pageSize) break;
            }
        }
        if (count($selectedArticles) < $pageSize) {
            $trendingArticles = $trendingArticles->shuffle();
            foreach ($trendingArticles as $article) {
                if ($selectedArticles[$article->articleId] ?? 0) continue;
                $selectedArticles[$article->articleId] = "TF_{$article->topic}"; //could be score
                if (count($selectedArticles) >= $pageSize) break;
            }
        }
        if (count($selectedArticles) < $pageSize) {
            $newArticles = $newArticles->shuffle();
            foreach ($newArticles as $article) {
                if ($selectedArticles[$article->articleId] ?? 0) continue;
                $selectedArticles[$article->articleId] = "NF_{$article->topic}"; //could be score
                if (count($selectedArticles) >= $pageSize) break;
            }
        }
        return $selectedArticles;
    }

    private function getPublicForyouArticles($sessionId, $sessionTime, $languages, $pageSize, $feedString = null)
    {
        $selectedArticles = [];
        $service = app(UserProfileService::class);
        $muteChannels = $service->getMutedChannels();
        $results = $this->getMoreHotArticles($sessionId, $sessionTime, $languages, [], false,  $feedString, $muteChannels);
        $results = $results->groupBy('topic');
        $forceMode = false;
        while (count($selectedArticles) < $pageSize) {
            $topics = $results->keys()->toArray();
            shuffle($topics);
            foreach ($topics as $topic) {
                $articles = $results->get($topic);
                foreach ($articles as $article) {
                    if ($selectedArticles[$article->articleId] ?? 0) continue;
                    if ((!$article->userFeed && !$article->userViews) || $forceMode) {
                        $selectedArticles[$article->articleId] = $topic;
                        break;
                    }
                    if (count($selectedArticles) >= $pageSize) break;
                }
                if (count($selectedArticles) >= $pageSize) break;
            }
            $forceMode = true;
        }
        return $selectedArticles;
    }

    public function getHotArticlesV2(
        string $sessionId,
        ?int $sessionTime,
        array $languages,
        array $excludeTopics,
        ?array $allTopics = null
    ): array {

        $newSession = false;
        if (!$sessionTime || $sessionTime < (time() - 3600) || $sessionTime > time()) {
            $sessionTime = time();
            DB::table('user_hot_session_feed')
                ->where('pid', $sessionId)
                ->where('session_time', '<', $sessionTime)
                ->delete();
            $newSession = true;
            $excludeTopics = [];
        }
        $newTopics = [];
        $more = false;
        $i = 0;
        foreach ($allTopics as $t) {
            $i++;
            if (in_array($t->id, $excludeTopics)) {
                continue;
            }

            if (count($newTopics) == 3) {
                if ($i < count($allTopics))
                    $more = true;
                break;
            }

            $newTopics[] = $t;
            $excludeTopics[] = $t->id;
        }
        if (!count($newTopics)) {
            abort(400, "No topics to serve");
        }
        $selectedArticles = [];
        $topics = [];
        $mainLang = strtolower($languages[0]);
        $langsString = implode("','", $languages);
        $langsString = "'$langsString'";
        $limitPerTopic = 5;
        $index = [];
        foreach ($newTopics as $t) {
            $articles  = [];
            $selectedArticlesString = implode("','", array_keys($selectedArticles));
            $selectedArticlesString = "'$selectedArticlesString'";
            $articlesFromTopic = DB::select(
                "SELECT articleId from new_foryou_articles_pool as p
                where topic = {$t->id} and
                is_hot = 1 and pool_type = 'n' and
                not exists (
                        select 1 from user_hot_session_feed as y
                        where y.pid = :pid1 and
                        y.session_time = :session_time and
                        y.article_id = p.articleId
                )
                and articleId not in ($selectedArticlesString)
                ORDER BY score DESC
                LIMIT 10
                ",
                ['pid1' => $sessionId, 'session_time' => $sessionTime]
            );
            $selected = 0;
            foreach ($articlesFromTopic as $a) {
                $i++;
                if (isset($selectedArticles[$a->articleId])) continue;
                $index[$a->articleId] = $t->id;
                $articles[] = $a->articleId;
                $selectedArticles[$a->articleId] = 1;
                if (count($articles) >= $limitPerTopic) {
                    break;
                }
            }
            $articlesString = implode(",", $articles);
            $topics["t_{$t->id}"] = [
                "id" => $t->id,
                'articles' => [],
                "name" => $t->{"name_$mainLang"},
                "more_url" => url("v3/api/v2/articles/hot?topic={$t->id}&articlesToIgnore=$articlesString")
            ];
        }
        //$newTopicsString = implode(',', $newTopics);
        $allArticles = $this->getArticles(array_keys($selectedArticles), false, true);
        foreach ($allArticles as $article) {
            $tid = $index[$article->uniqueID];
            $topics["t_{$tid}"]['articles'][] = $article;
        }
        $excludeTopicsString = implode(',', $excludeTopics);
        return [
            "newSession" => $newSession,
            "more" => $more,
            "more_url" => url("v3/api/v2/articles/hot?topics=$excludeTopicsString&sessionTime=$sessionTime&sessionId=$sessionId"),
            "topics" => array_values($topics)
        ];
    }


    public function getHotArticlesV2PerTopic(
        array $languages,
        int $topic,
        ?array $articlesToIgnore = null
    ): array {
        $newSession = false;
        $more = false;
        $i = 0;
        $selectedArticles = [];
        $mainLang = strtolower($languages[0]);
        $langsString = implode("','", $languages);
        $langsString = "'$langsString'";
        $limitPerTopic = 15;
        $articles  = [];
        $selectedArticlesString = implode("','", array_keys($articlesToIgnore));
        $selectedArticlesString = "'$selectedArticlesString'";
        $articlesFromTopic = DB::select(
            "SELECT articleId from new_foryou_articles_pool as p
                where topic = {$topic} and
                is_hot = 1 and
                pool_type = 'n' and
                articleId not in ($selectedArticlesString)
                ORDER BY score DESC
                LIMIT $limitPerTopic
            "
        );
        foreach ($articlesFromTopic as $a) {
            $articles[] = $a->articleId;
        }
        $allArticles = $this->getArticles($articles, false, true);

        $t = $this->getTopic($topic);
        $topics[] = [
            "id" => $t->id,
            "name" => $t->{"name_$mainLang"},
            'articles' => $allArticles,
        ];
        return [
            "newSession" => $newSession,
            "more" => $more,
            "topics" => $topics
        ];
    }


    function getTopic($topicId)
    {
        return cache("nw_topic_$topicId", function () use ($topicId) {
            return DB::table('topics')->where('id', $topicId)->limit(1)->get()->first();
        }, 10);
    }

    public function getFeedArticles(
        $feedId,
        ?array $languages,
        ?string $lastID,
        ?string $firstID,
        $contentTypes = ['a']
    ): array {

        $articleIdToAppend = false;
        $query = DB::table('segment_feed_articles')
            ->select('article_unique_id as unique_id', 'published_date')
            ->distinct()
            ->where('enabled', 1)
            ->where('published_date', '<=', date('Y-m-d H:i:s'))
            ->whereIn('language', $languages);
        if ($lastID) {
            $query = $query
                ->where('published_date', "<", $this->getNextArticleDateFromBottom($lastID))
                ->orderBy('published_date', 'DESC');
        } elseif ($firstID) {
            $query = $query
                ->where('published_date', ">", $this->getNextArticleDateFromTop($firstID))
                ->orderBy('published_date', 'DESC');
        } else {
            switch ($languages[0]) {
                case 'en':
                    $articleIdToAppend = nw_bunker('article', 'covid_en');
                    break;
                case 'zh':
                    $articleIdToAppend = nw_bunker('article', 'covid_zh');
                    break;
                case 'ms':
                    $articleIdToAppend = nw_bunker('article', 'covid_ms');
                    break;
            }
            $query = $query->orderBy('published_date', 'DESC');
        }
        $query = $query->whereIn('content_type', $contentTypes);
        $query = $query->where('feed_id', $feedId);
        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
        }
        $results = $query->take($this->limit)->pluck('unique_id')->toArray();
        $ids = [];
        if ($articleIdToAppend) {
            if(is_array($articleIdToAppend)) {
                $ids = $articleIdToAppend;
            } else{
                $ids[] = $articleIdToAppend;
            }
        }
        foreach ($results as $id) {
            $ids[] = $id;
        }
        $articles = $this->getArticles($ids, false, true);
        if ($articleIdToAppend) {
            $articles[0]->kind = "corona_statistics";
            $articles[0]->extra = $this->getCoronaStats($languages[0]);
        }
        return $articles;
    }

    public function getCoronaFeedStat($language)
    {
        $articleIdToAppend = nw_bunker('article', 'covid_en');
        $corona = (object) $this->getCoronaStats($language);

        return [
            'name' => $corona->countries[1]['name'],
            'articleID' => $articleIdToAppend,
            'updatedAt' => $corona->updatedDate,
            'char_id' => $corona->malaysiaChartId,
            'statistics' => $corona->countries[1]['statistics']
        ];
    }

    public function getFeedArticlesV2(
        $feedId,
        ?array $languages,
        ?string $lastUniqueID,
        ?string $firstUniqueID,
        $contentTypes = ['a'],
        $profileId
    ): array {

        $articleIdToAppend = false;
        $query = DB::table('segment_feed_articles')
            ->select('article_unique_id as unique_id', 'published_date', 'content_type')
            ->distinct()
            ->where('published_date', '<=', date('Y-m-d H:i:s'))
            ->whereIn('language', $languages);

        $isDebug = Auth::user()->isDebug();
        if (!$isDebug) {
            $query = $query->where('enabled', 1);
        }
        $service = app(UserProfileService::class);
        $channels = $service->getMutedChannels();
        if ($channels && count($channels) > 0)
            $query = $query->whereNotIn('channel_id', $channels);

        if ($lastUniqueID) {
            $query = $query
                ->where('published_date', "<", $this->getEntityPublishedDate($lastUniqueID))
                ->orderBy('published_date', 'DESC');
        } elseif ($firstUniqueID) {
            $query = $query
                ->where('published_date', ">", $this->getEntityPublishedDate($firstUniqueID))
                ->orderBy('published_date', 'DESC');
        } else {
            switch ($languages[0]) {
                case 'en':
                    $articleIdToAppend = array_merge(
                        nw_bunker('article', 'covid_en'),
                        nw_bunker('article', 'malaysiakini_covid_en')
                    );
                    break;
                case 'zh':
                    $articleIdToAppend = array_merge(
                        nw_bunker('article', 'covid_zh'),
                        nw_bunker('article', 'malaysiakini_covid_zh')
                    );
                    break;
                case 'ms':
                    $articleIdToAppend = array_merge(
                        nw_bunker('article', 'covid_ms'),
                        nw_bunker('article', 'malaysiakini_covid_ms')
                    );
                    break;
            }
            $query = $query->orderBy('published_date', 'DESC');
        }
        $query = $query->whereIn('content_type', $contentTypes);
        $query = $query->where('feed_id', $feedId);
        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
        }
        $results = $query->take($this->limit)->get();
        $ids = [];
        $vids = [];
        if ($articleIdToAppend) {
            $ids[] = $articleIdToAppend[0];
            if (isset($articleIdToAppend[1]))  $ids[] = $articleIdToAppend[1];
        }
        foreach ($results as $result) {
            if ($result->content_type == 'v') {
                $vids[] = $result->unique_id;
            } else if ($result->content_type == 'a') {
                $ids[] = $result->unique_id;
            }
        }
        $articles = collect($this->getArticles($ids, false, true, [], true))->keyBy('uniqueID');
        // if ($articleIdToAppend[0] ?? false) {
        //     $articles->get($articleIdToAppend[0])->kind = "parliament_statistics";
        //     $articles->get($articleIdToAppend[0])->extra = $this->getParliamentStats($languages[0]);
        // }
        if ($articleIdToAppend[0] ?? false) {
            $articles->get($articleIdToAppend[0])->kind = "corona_statistics";
            $articles->get($articleIdToAppend[0])->extra = $this->getCoronaStats($languages[0]);
        }

        $videoRepo = app(VideoRepository::class);
        $videos = $videoRepo->getVideosById($vids, $profileId)->keyBy('unique_id');
        $objects = [];
        if ($articleIdToAppend[0] ?? false) {
            $objects[] = $articles->get($articleIdToAppend[0]);
        }

        if ($articleIdToAppend[1] ?? false) {
            $objects[] = $articles->get($articleIdToAppend[1]);
        }
        foreach ($results as $result) {
            if ($result->content_type == 'v') {
                $objects[] =  $videos->get($result->unique_id);
            } else if ($result->content_type == 'a') {
                $objects[] = $articles->get($result->unique_id);
            }
        }
        return $objects;
    }


    public function getPnArticles(?string $profileId, $languages, $mainLanguage, $latestDate = null): ?array
    {

        $today = Carbon::now();
        $clean = true;
        if ($latestDate == null) {
            $latestDate = Carbon::now();
        } else {
            $clean = false;
            $latestDate = Carbon::parse($latestDate);
            if (!$latestDate) throw new \Exception("invalid date");
        }
        $pns = $profileId ? $this->getProfilePnArticles($profileId, $latestDate,  $mainLanguage, $clean ) : null;
        $ids = [];
        $more = false;
        $default = $pns && count($pns);

        if ($latestDate->format("Y-m-d") == $today->format("Y-m-d")) {
            $more = true;
            $ids = $pns ?? [];
        } else if ($latestDate->greaterThan($today->subDays(30))) {
            $more = true;
            $ids = $pns ?? $this->getDefaultPn($languages, $latestDate);
        } else if ($latestDate->equalTo($today->subDays(30))) {
            $more = false;
            $ids = $pns ?? $this->getDefaultPn($languages, $latestDate);
        } else {
            $more = false;
        }
        $articles = $this->getArticles($ids, false, $default, [], false, false);
        $res = [
            "date" => $latestDate->format("Y-m-d"),
            "articles" => $ids && count($ids) ? $articles : [],
            "more" => $more,
            "previous_date" => $latestDate->subDay()->format("Y-m-d")
        ];
        return $res;
    }


    // field -> date, score, mixed
    //request-mode -> newer, older
    public function getFeedArticlesV3(
        $feedId,
        $profileId,
        $firebaseId,
        $page = 1,
        $contentTypes = ['a'],
        ?string $lastDate = null,
        ?string $firstDate = null,
        ?array $languages = ['en'],
        ?array $historyArticles = null,
        ?string $field = 'date',
        ?string $requestMode = 'older',
        bool $newCovid = false
    ): array {

        $cdnUrl = nw_bunker('gcloud', 'cdn_newswav_url', 'https://cdn.newswav.com/');

        if ($feedId == 3) {
            /** @var PollService $pollService */
            $pollService = app(PollService::class);
            $results = $pollService->getActivePolls($page, $firebaseId, $languages);
            return [
                'articles' => $results['articles'],
                'more' => $results['more'],
                'newSession' => false,
                'firstDate' => $firstDate,
                'lastDate' => $lastDate,
                'lastScore' => 0,
                'feedId' => $feedId,
                'prePage' => $page,
                'page' => $page + 1,
                'contentTypes' => implode(',', $contentTypes),
                'languages' => implode(',', $languages),
                'field' => 'page',
                'requestMode' => $requestMode
            ];
        }
        $query = DB::table('segment_feed_articles')
            ->select('article_unique_id as unique_id', 'published_date', 'content_type')
            ->distinct()
            ->whereIn('language', $languages);

        $isDebug = Auth::user()->isDebug();
        if (!$isDebug) {
            $query = $query->where('enabled', 1);
        }
        $articleIdToAppend = false;
        $service = app(UserProfileService::class);
        $channels = $service->getMutedChannels();
        if ($channels && count($channels) > 0)
            $query = $query->whereNotIn('channel_id', $channels);

        if ($requestMode == 'older') {
            $query = $query
                ->where('published_date', '<', ($lastDate ?? date('Y-m-d H:i:s')))
                ->orderBy('published_date', 'DESC');
        } else {
            if ($firstDate) {
                $query = $query
                    ->where('published_date', '>', $firstDate ?? date('Y-m-d H:i:s'));
            }
            $query = $query
                ->orderBy('published_date', 'ASC');
        }

        if ($historyArticles) {
            $query = $query->whereNotIn('article_unique_id', $historyArticles);
        }

        /** @var NwUser|FbUser|NwUserV4|NwWebUser $user */
        $user = Auth::user();
        $firebaseId = $user->getFirebaseId();
        if($firebaseId){
            $hiddenContents = $user->getUserHiddenContent();
            if($hiddenContents && count($hiddenContents)){
                $query = $query->whereNotIn('article_unique_id', $hiddenContents);
            }
        }
        if ($feedId == 1 && $lastDate == null && $firstDate == null) {
            // switch ($languages[0]) {
            //     case 'en':
            //         $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_1_en');
            //         break;
            //     case 'zh':
            //         $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_1_zh');
            //         break;
            //     case 'ms':
            //         $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_1_ms');
            //         break;
            // }
        } else if ($feedId == 7 && $lastDate == null && $firstDate == null) {
            $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_7');
        }  else if ($feedId == 8 && $lastDate == null && $firstDate == null) {
            $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_8');
        } else if ($feedId == 10 && $lastDate == null && $firstDate == null) {
            $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_10');
        } else if (SarawakElectionServiceImpl::SARAWAK_ELECTION_FEED_ID == $feedId && $lastDate == null && $firstDate == null) {
            switch ($languages[0]) {
                case 'en':
                    $articleIdToAppend = [ SarawakElectionServiceImpl::SARAWAK_ELECTION_EN_ARTICLE_ID ];
                    break;
                case 'ms':
                    $articleIdToAppend = [ SarawakElectionServiceImpl::SARAWAK_ELECTION_MS_ARTICLE_ID ];
                    break;
                case 'zh':
                    $articleIdToAppend = [ SarawakElectionServiceImpl::SARAWAK_ELECTION_ZH_ARTICLE_ID ];
                    break;
            }
        } else if ($feedId == 13 && $lastDate == null && $firstDate == null) {
            switch ($languages[0]) {
                case 'en':
                    $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_13_en');
                    break;
                case 'zh':
                    $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_13_zh');
                    break;
                case 'ms':
                    $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_13_ms');
                    break;
            }

        } else if ($feedId == 14 && $lastDate == null && $firstDate == null) {
            switch ($languages[0]) {
                case 'en':
                    $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_14_en');
                    break;
                case 'zh':
                    $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_14_zh');
                    break;
                case 'ms':
                    $articleIdToAppend = nw_bunker('article', 'append_by_feed_id_14_ms');
                    break;
            }
        } else if ($feedId == MelakaElectionServiceImpl::MELAKA_ELECTION_FEED_ID  && $lastDate == null && $firstDate == null ) {
            switch ($languages[0]) {
                case 'en':
                    $articleIdToAppend = [ MelakaElectionServiceImpl::MELAKA_ELECTION_EN_ARTICLE_ID ];
                    break;
                case 'ms':
                    $articleIdToAppend = [ MelakaElectionServiceImpl::MELAKA_ELECTION_MS_ARTICLE_ID ];
                    break;
                case 'zh':
                    $articleIdToAppend = [ MelakaElectionServiceImpl::MELAKA_ELECTION_ZH_ARTICLE_ID ];
                    break;
            }

        }

        $pinService = app(PinItemService::class);


        $pinned = $pinService->getPinned('F_F_'.$feedId, ['article', 'video']);


        if (count($pinned) > 0 ) {
            if ($page == 1){
                if (!$articleIdToAppend){
                    $articleIdToAppend = $pinned;
                }else{
                    $articleIdToAppend = array_unique(array_merge($articleIdToAppend, $pinned));
                }
            }
            $query->whereNotIn('article_unique_id', $pinned);

        }

        $query = $query->whereIn('content_type', $contentTypes);

        $query = $query->where('feed_id', $feedId);

      ///  $query = $query->where('feed_id', $feedId);

        if($articleIdToAppend && count($articleIdToAppend) > 0){
            $query = $query->whereNotIn('article_unique_id', $articleIdToAppend);
        }

        $disabledChannels = getDisabledChannelsFromCache();
        if ($disabledChannels) {
            $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
        }

        $results = $query->take($this->limit)->get();

        $ids = [];
        $vids = [];
        if (!count($results) && (!$articleIdToAppend)) {
            return [
                'articles' => [],
                'more' => false,
                'newSession' => false,
                'firstDate' => $firstDate,
                'lastDate' => $lastDate,
                'lastScore' => 0,
                'feedId' => $feedId,
                'page' => $page + 1,
                'contentTypes' => implode(',', $contentTypes),
                'languages' => implode(',', $languages),
                'field' => $field,
                'requestMode' => $requestMode
            ];
        }


        if (($requestMode == 'older') || !$lastDate) {
            $timestamp = $results
                ->pluck('published_date')
                ->map(function ($item) {
                    return Carbon::parse($item)->timestamp;
                })
                ->min();
            $lastDate = Carbon::createFromTimestamp($timestamp)->toDateTimeString();
        }
        if (($requestMode == 'newer') || !$firstDate) {
            $timestamp = $results
                ->pluck('published_date')
                ->map(function ($item) {
                    return Carbon::parse($item)->timestamp;
                })
                ->max();
            $firstDate = Carbon::createFromTimestamp($timestamp)->toDateTimeString();
        }
        if (isset($articleIdToAppend) && is_array($articleIdToAppend)) {
            // $ids[] = $articleIdToAppend[0];
            // if (isset($articleIdToAppend[1]))  $ids[] = $articleIdToAppend[1];
            // if (isset($articleIdToAppend[2]))  $ids[] = $articleIdToAppend[2];
            // if (isset($articleIdToAppend[3]))  $ids[] = $articleIdToAppend[3];
            //remove limit 3 on pinned
            $ids = $articleIdToAppend;
        }
        foreach ($results as $result) {
            if ($result->content_type == 'v') {
                $vids[] = $result->unique_id;
            } else if ($result->content_type == 'a') {
                $ids[] = $result->unique_id;
            }
        }



        if ($feedId == 3) {
            $pollService = app(PollService::class);
            $articles =  collect($pollService->getPollsByArticleIds($ids, $firebaseId))->keyBy('uniqueID');
        } else {
            $obtainedArticles = $this->getArticles($ids, false, true, [], true);
            $articles = count($ids) ? collect($obtainedArticles)->keyBy('uniqueID') : collect();
        }

        // if ($articleIdToAppend[0] ?? false) {
        //     $articles->get($articleIdToAppend[0])->kind = "parliament_statistics";
        //     $articles->get($articleIdToAppend[0])->extra = $this->getParliamentStats($languages[0]);
        // }

        if ($feedId == 1 && ($articleIdToAppend[0] ?? false)) {
            // $articles->get($articleIdToAppend[0])->kind = "corona_statistics";
            // $articles->get($articleIdToAppend[0])->extra = $newCovid ? $this->getCoronaStats2($languages[0]) : $this->getCoronaStats($languages[0]);
        } else if ($feedId == 7 && ($articleIdToAppend[0] ?? false)) {
            $articles->get($articleIdToAppend[0])->kind = "merdeka";
            $languages = ['en'];
            $articles->get($articleIdToAppend[0])->extra = $this->getMerdekaData($languages[0]);
        } else if ($feedId == 11 && ($articleIdToAppend[0] ?? false)) {
            $repo = app(SarawakElectionService::class);
            $articles->get($articleIdToAppend[0])->url = $articles->get($articleIdToAppend[0])->url."?r=".rand(0,999)."&x=".rand(0,9999);
            $articles->get($articleIdToAppend[0])->canonicalURL = $articles->get($articleIdToAppend[0])->canonicalURL."?r=".rand(0,999)."&x=".rand(0,9990);
            $articles->get($articleIdToAppend[0])->kind = 'election_sarawak';
            $articles->get($articleIdToAppend[0])->extra = $repo->getElectionExtra($languages, $profileId ?? Str::random());
        } else if ($feedId == 14 && ($articleIdToAppend[0] ?? false)) {
            $repo = app(OlympicService::class);
            $articles->get($articleIdToAppend[0])->kind = "olympic2021";
            $articles->get($articleIdToAppend[0])->extra = $repo->getOlympicExtra($languages[0] ?? 'en');
        } else if ($feedId == MelakaElectionServiceImpl::MELAKA_ELECTION_FEED_ID && ($articleIdToAppend[0] ?? false)) {
            $repo = app(MelakaElectionService::class);
            $articles->get($articleIdToAppend[0])->kind = 'election';
            $articles->get($articleIdToAppend[0])->extra = $repo->getElectionExtra($languages, $profileId ?? Str::random());
        }

        $vidpinned = [];

        if ($page == 1){
            $vidpinned = array_merge(array_filter($pinned, function($i) {
                return Str::startsWith($i, 'V');
            }), $vidpinned);
            $vids = array_merge($vidpinned, $vids);
        }

        $videoRepo = app(VideoRepository::class);
        $videos = count($vids) ? $videoRepo->getVideosById($vids, $profileId)->keyBy('unique_id') : collect();

        $objects = [];
        // if ($articleIdToAppend[0] ?? false) {
        //     $objects[] = $articles->get($articleIdToAppend[0]);
        // }
        // if ($articleIdToAppend[1] ?? false) {
        //     $objects[] = $articles->get($articleIdToAppend[1]);
        // }
        // if ($articleIdToAppend[2] ?? false) {
        //     $objects[] = $articles->get($articleIdToAppend[2]);
        // }
        // if ($articleIdToAppend[3] ?? false) {
        //     $objects[] = $articles->get($articleIdToAppend[3]);
        // }
        if (isset($articleIdToAppend) && is_array($articleIdToAppend)){
            foreach ($articleIdToAppend as $aId){ //bruhhh
                $p = $articles->get($aId);

                if ($p){
                    $p->pinned = 1;
                    $objects[] = $p;
                }

            }
        }

        if ($page == 1){
            foreach($vidpinned as $p){
                $p = $videos->get($p);

                if ($p){
                    $p->pinned = 1;
                    $objects[] = $p;
                }

            }
        }

        foreach ($results as $index => $result) {
            if ($result->content_type == 'v') {
                $objects[] =  $videos->get($result->unique_id);
            } else if ($result->content_type == 'a') {
                $objects[] = $articles->get($result->unique_id);
            }
        }

        $fix = false;
        $length = count($objects);
        for($i = 0; $i < $length; $i++){
            if(!$objects[$i]){
                $fix = true;
                unset($objects[$i]);
                --$this->limit;
                Log::info($results);
                Log::info($feedId. " - page ".$page. " index ".$i );
                Log::info($articleIdToAppend);
                //log_emergency('Unexpected null article',);
            }
        }
        if($fix){
            $objects = array_values($objects);
        }


        $more =  (count($objects) >= $this->limit);
        return [
            'articles' => $objects,
            'more' => $more,
            'newSession' => false,
            'firstDate' => $firstDate,
            'lastDate' => $lastDate,
            'lastScore' => 0,
            'feedId' => $feedId,
            'page' => $page + 1,
            'contentTypes' => implode(',', $contentTypes),
            'languages' => implode(',', $languages),
            'field' => $field,
            'requestMode' => $requestMode
        ];
    }


    public function searchArticlesV4(string $q, array $languages, ?string $page, $onlyIds = false, $size = null, $sortPublishedDate = false): array
    {
        $disabledChannels = getDisabledChannelsFromCache();

        $q = str_replace(":","", $q);

        $parts = $this->getSearchQuery($q);

        if (!count($parts) && $onlyIds) {
            throw new NwInvalidSearchKeyword;
        }

        if (!count($parts)) {
            return $this->getHotSearchArticles($languages);
        }

        if ($size){
            $pageSize = $size;
            $ids = $this->searchArticlePerQueryV3($parts, $q,  $languages, $disabledChannels, $page, $pageSize, $sortPublishedDate);
        }else{
            $pageSize = ((int) $page > 1) ? 30 : 10;
            $ids = $this->searchArticlePerQueryV2($parts, $q,  $languages, $disabledChannels, $page, $pageSize);
        }

        // Filter out hidden articles for authenticated users
        /** @var NwUser|FbUser|NwUserV4|NwWebUser $user */
        $user = Auth::user();
        $firebaseId = $user->getFirebaseId();
        if($firebaseId && count($ids) > 0){
            $hiddenContents = $user->getUserHiddenContent();
            if($hiddenContents && count($hiddenContents)){
                $ids = array_values(array_diff($ids, $hiddenContents));
            }
        }

        if($onlyIds){
            return $ids;
        }
        return $this->getArticles($ids, false, true);
    }


    public function searchTopicsV2(string $q, ?string $lastID): array
    {
        $client = ClientBuilder::create()->setHosts([config('elasticsearch.elastic_search_url')])->build();
        $results = [];
        $start = 0;
        $size = 100;
        $parts = $this->getSearchQuery($q);

        if (count($parts)) {
            $params = $this->buildEsSearchReqForTopics($q, $start, $size);
            try{
                $response = $client->search($params);
            }catch(BadRequest400Exception $e){
                //no data in index, return empty array
                return $results;
            }

            foreach ($response['hits']['hits'] as $obj) {
                $results[] = $obj['_source']['id'];
            }
        } else {
            //log_emergency('wired case, empty parts ');
        }
        return $results;
    }

    public function searchPublishersV2(string $q, ?string $lastID): array
    {
        $client = ClientBuilder::create()->setHosts([config('elasticsearch.elastic_search_url')])->build();
        $results = [];
        $start = 0;
        $size = 100;
        $parts = $this->getSearchQuery($q);

        if (count($parts)) {
            $params = $this->buildEsSearchReqForPublishers($q, $start, $size);
            try{
                $response = $client->search($params);
            }catch(BadRequest400Exception $e){
                //no data in index, return empty array
                return $results;
            }

            // dd($response);
            foreach ($response['hits']['hits'] as $obj) {
                $results[] = $obj['_source']['id'];
            }
        } else {
            //log_emergency('wired case, empty parts '.$q);
        }
        return $results;
    }

    public function searchPublishersV3(string $q, $page = 1, $size): array
    {
        $client = ClientBuilder::create()->setHosts([config('elasticsearch.elastic_search_url')])->build();
        $results = [];
        $start = ($page - 1) * $size;
        $parts = $this->getSearchQuery($q);
        if (count($parts)) {
            $params = $this->buildEsSearchReqForPublishers($q, $start, $size);
            try{
                $response = $client->search($params);
            }catch(BadRequest400Exception $e){
                //no data in index, return empty array
                return $results;
            }

            // dd($response);
            foreach ($response['hits']['hits'] as $obj) {
                $results[] = $obj['_source']['id'];
            }
        } else {
            //log_emergency('wired case, empty parts '.$q);
        }
        return $results;
    }


    private function buildEsSearchReqForTopics($query, $from, $size)
    {
        return [
            'index' => config('elasticsearch.es_topics_index'),
            'body' => [
                "from" => $from,
                "size" => $size,
                "_source" => [
                    "id",
                    // "publishedDate",
                    // "title",
                ],
                "query" => [
                    "bool" => [
                        "minimum_should_match" => 1,
                        "must" => [
                            [
                                "terms" => [
                                    "enabled" =>  [ 1 ]
                                ]
                            ]
                        ],
                        "should" => [
                            [
                                "multi_match" => [
                                    "type" => "phrase",
                                    "fields" => [
                                        "name_en",
                                        "name_ms",
                                        "name_zh",
                                    ],
                                    "query" => "$query",
                                ]
                            ],
                            [
                                "multi_match" => [
                                    "fields" => [
                                        "name_en",
                                        "name_ms"
                                        // exclude chinese lang to avoid individual character getting matched
                                    ],
                                    "query" => "$query",
                                    "fuzziness" => "AUTO",
                                    "auto_generate_synonyms_phrase_query" => true
                                ]
                            ],
                        ]
                    ]
                ]
            ]
        ];
    }

    private function buildEsSearchReqForPublishers($query, $from, $size)
    {
        return [
            'index' => config('elasticsearch.es_publishers_index'),
            'body' => [
                "from" => $from,
                "size" => $size,
                "_source" => [
                    "id",
                    // "publishedDate",
                    // "title",
                ],
                "query" => [
                    "bool" => [
                        "minimum_should_match" => 1,
                        "must" => [
                            [
                                "terms" => [
                                    'enabled' => [1]
                                ]
                            ]
                        ],
                        "should" => [
                            [
                                "multi_match" => [
                                    "type" => "phrase",
                                    "fields" => [
                                        "name",
                                    ],
                                    "query" => "$query",
                                ]
                            ],
                            [
                                "multi_match" => [
                                    "fields" => [
                                        "name",
                                    ],
                                    "query" => "$query",
                                    "auto_generate_synonyms_phrase_query" => true
                                ]
                            ],
                            [
                                "wildcard" => [
                                    "name" => [
                                        "value" => "*$query*",
                                    ]
                                ]
                            ]
                        ],
                    ]
                ]
            ]
        ];
    }

    private function getProfilePnArticles(string $profileId,  $latestDate, $mainLanguage, $clean = false): ?array
    {

        $pns = DB::connection('pn_cluster')
            ->table('pn_sent2')
            ->select('created_at', 'article_id')
            ->where('profile_id', $profileId)
            ->orderByDesc('created_at')
            ->get()
            ->map(function ($item) {
                $item->day = Carbon::createfromFormat('Y-m-d H:i:s', $item->created_at)->format('Y-m-d');
                $item->i = 0;
                return $item;
            });

        //no need to inject instants pn since we store instant pn
        // $instantPns = DB::connection('pn_cluster')
        //     ->table('custom_push')
        //     ->select('createdDate as created_at', 'articleUniqueID as article_id')
        //     ->where('version', 15)
        //     ->where('language',  $mainLanguage)
        //     ->where('createdDate','>=',  Carbon::now()->subDays(10)->format('Y-m-d H:i:s'))
        //     ->orderByDesc('id')
        //     ->get()
        //     ->map(function ($item) {
        //         $item->day = Carbon::createfromFormat('Y-m-d H:i:s', $item->created_at)->format('Y-m-d');
        //         $item->i = 1;
        //         return $item;
        //     });

        $duplicates = cache_pn_feed_get($profileId, $clean);


        $pns = $pns
        // ->merge($instantPns)
        ->filter(function($value) use (&$duplicates){

            if(empty($value->article_id)) return false;
            if(isset($duplicates[$value->article_id])) return false;
            $duplicates[$value->article_id] = 1;
            return true;
        })
        ->sortByDesc('created_at')
        ->groupBy('day');


        $ids = $pns->has($latestDate->format('Y-m-d')) ? ($pns->get($latestDate->format('Y-m-d'))->pluck('article_id')->toArray()) : null;
        cache_pn_feed_update( $profileId, $ids);
        return $ids ;
    }

    private function getDefaultPn($languages, $latestDate)
    {
        $query = DB::connection('pn_cluster')
            ->table('custom_push')
            ->where('articleUniqueID','!=', '')
            ->select('articleUniqueID', 'language', 'sentTotalUser')
            ->limit(200)
            ->orderByDesc('id');

        if ($latestDate != null) {
            $query =  $query->whereDate('createdDate', '=', $latestDate);
        }

        $pns = $query->get();
        return $pns
            ->filter(function ($item) use ($languages) {
                return in_array($item->language, $languages);
            })
            ->sortByDesc('sentTotalUser')
            ->take(20)
            ->pluck('articleUniqueID')
            ->toArray();
    }

    private function getCoronaStats($lang)
    {

        $tableName = "corona_stats_2";
        $q = DB::table($tableName)->select('stats_date')->distinct()->orderByDesc('stats_date')
        ->limit(2);
        if(Carbon::now()->hour < 12){
            $q = $q->offset(1);
        }
        $lastDates = $q->get();
        $day1Data = DB::table($tableName)->whereNull('city')->where('stats_date', $lastDates[0]->stats_date)
            ->get()
            ->map(function ($obj) {
                $obj->country = trim($obj->country);
                return $obj;
            })
            ->keyBy('country');
        $day2Data = DB::table($tableName)->whereNull('city')->where('stats_date', $lastDates[1]->stats_date ?? '2000-01-30')
            ->get()
            ->map(function ($obj) {
                $obj->country = trim($obj->country);
                return $obj;
            })
            ->keyBy('country');

        $shares = DB::table('article_stats')->whereIn('article_id', nw_bunker('article', 'covid_all'))->sum('shares');
        $malaysiaChartId = '1526844';
        $globalChartId = '1530528';
        $totalCountries = ($day1Data->count() - 5) . '';
        $top5Countries  = $day1Data
            ->sortByDesc('confirmed')
            ->take(5)
            ->map(function ($item) use ($day2Data) {
                $x = new \StdClass;
                $x->name =  $item->country;
                $x->confirmed =  $this->decorateNumber($item->confirmed) . "";
                $x->death =  $this->decorateNumber($item->death) . "";
                $x->cured =  $this->decorateNumber($item->cured) . "";
                if (isset($day2Data->get($item->country)->confirmed)){
                        $new = $item->confirmed - $day2Data->get($item->country)->confirmed;
                }else{
                        $new = 0;
                }
                $x->new = ($new) > 0 ? $this->decorateNumber($new) : "0";
                return $x;
            })
            ->values()
            ->all();

        switch ($lang) {
            case 'zh':
                $malaysiaChartId = '1534728';
                $globalChartId = '1534736';
                break;
            case 'ms':
                $malaysiaChartId = '1534725';
                $globalChartId = '1534732';
                break;
        }
        $actions = DB::table('corona_actions')->orderBy('id')->get()->map(function ($item) use ($lang) {

            $obj = new \stdClass;
            if ($item->type == 'article') {
                $articleObj = json_decode($item->value);
                $obj->value = $articleObj->{$lang};
            } else {
                $obj->value = $item->value;
            }
            $obj->name = $item->{'name_' . $lang};
            $obj->type = $item->type;
            $obj->image = $item->image;
            $obj->color = $item->color;

            return $obj;
        })->toArray();
        $result = [
            "updatedDate" => $day1Data->get('China')->updated_from_source,
            "malaysiaChartId" => $malaysiaChartId,
            "globalChartId" => $globalChartId,
            "totalCountries" => $totalCountries,
            "shares" => $shares,
            "top5Countries" => $top5Countries,
            "actions" => $actions,
            "countries" => [
                [
                    "name" => "Global",
                    "char_id" => $globalChartId,
                    "statistics" => [
                        [
                            "name" =>  $this->translate("Confirm", $lang),
                            "total" => $day1Data->sum('confirmed'),
                            "added" => $day1Data->sum('confirmed') - ($day2Data->sum('confirmed') ?? 0)  > 0 ? $day1Data->sum('confirmed') - ($day2Data->sum('confirmed') ?? 0) : 0
                        ],
                        [
                            "name" =>  $this->translate("Suspect", $lang),
                            "total" => $day1Data->sum('suspected'),
                            "added" => $day1Data->sum('suspected') - ($day2Data->sum('suspected') ?? 0) > 0 ? $day1Data->sum('suspected') - ($day2Data->sum('suspected') ?? 0) : 0
                        ],
                        [
                            "name" =>  $this->translate("Death", $lang),
                            "total" => $day1Data->sum('death'),
                            "added" => $day1Data->sum('death') - ($day2Data->sum('death') ?? 0) > 0 ? $day1Data->sum('death') - ($day2Data->sum('death') ?? 0) : 0
                        ],
                        [
                            "name" =>  $this->translate("Cured", $lang),
                            "total" => $day1Data->sum('cured'),
                            "added" => $day1Data->sum('cured') - ($day2Data->sum('cured') ?? 0) > 0 ? $day1Data->sum('cured') - ($day2Data->sum('cured') ?? 0) : 0
                        ]
                    ]
                ],
                [
                    "name" => "Malaysia",
                    "char_id" => $malaysiaChartId,
                    "statistics" => [
                        [
                            "name" =>  $this->translate("Confirm", $lang),
                            "total" => $day1Data->get('Malaysia')->confirmed,
                            "added" => $day1Data->get('Malaysia')->confirmed - ($day2Data->get('Malaysia')->confirmed ?? 0) > 0 ?  $day1Data->get('Malaysia')->confirmed - ($day2Data->get('Malaysia')->confirmed ?? 0) : 0
                        ],
                        [
                            "name" =>  $this->translate("Suspect", $lang),
                            "total" => $day1Data->get('Malaysia')->suspected,
                            "added" => $day1Data->get('Malaysia')->suspected - ($day2Data->get('Malaysia')->suspected ?? 0) > 0 ? $day1Data->get('Malaysia')->suspected - ($day2Data->get('Malaysia')->suspected ?? 0) : 0
                        ],
                        [
                            "name" =>  $this->translate("Death", $lang),
                            "total" => $day1Data->get('Malaysia')->death,
                            "added" => $day1Data->get('Malaysia')->death - ($day2Data->get('Malaysia')->death ?? 0) > 0 ?  $day1Data->get('Malaysia')->death - ($day2Data->get('Malaysia')->death ?? 0) : 0
                        ],
                        [
                            "name" =>   $this->translate("Cured", $lang),
                            "total" => $day1Data->get('Malaysia')->cured,
                            "added" => $day1Data->get('Malaysia')->cured - ($day2Data->get('Malaysia')->cured ?? 0) > 0 ? $day1Data->get('Malaysia')->cured - ($day2Data->get('Malaysia')->cured ?? 0) : 0
                        ]
                    ]
                ]
            ]
        ];

        if ($this->removeSuspectCorona) {
            unset($result['countries'][0]['statistics'][1]);
            unset($result['countries'][1]['statistics'][1]);
            $result['countries'][0]['statistics'] = array_values($result['countries'][0]['statistics']);
            $result['countries'][1]['statistics'] = array_values($result['countries'][1]['statistics']);
        }
        return $result;
    }


    private function getCoronaStats2($lang)
    {
        $tableName = "corona_stats_2";
        return $this->getCoronaStatsFromCache($tableName, $lang);
    }

    private function getCoronaStatsFromCache($tableName, $lang){
        return cache("111corona_stats_3_$lang", function () use($tableName, $lang){
            $q = DB::table($tableName)->select('stats_date')
                ->where('country','<>','Malaysia') // malaysia will be inserted by other function. so best to based on non malaysia country
                ->distinct()->orderByDesc('stats_date')
            ->limit(2);

            // if(Carbon::now()->hour < 8){
            //     $q = $q->offset(1);
            // }

            $lastDates = $q->get();

            $shares = DB::table('article_stats')->whereIn('article_id', ['A2001_UktsGg', 'A2001_W0EyTj', 'A2001_K3I5Kc', 'covid19__en', 'covid19__zh', 'covid19__ms'])->sum('shares');

            $malaysiaChartId = '1526844';
            $globalChartId = '1530528';
            switch ($lang) {
                case 'zh':
                    $malaysiaChartId = '1534728';
                    $globalChartId = '1534736';
                    break;
                case 'ms':
                    $malaysiaChartId = '1534725';
                    $globalChartId = '1534732';
                    break;
            }

            $actions = DB::table('corona_actions_2')->where('enabled', 1)->orderBy('id')->get()->map(function ($item) use ($lang) {
                $obj = new \stdClass;
                if ($item->type == 'article') {
                    $articleObj = json_decode($item->value);
                    $obj->value = $articleObj->{$lang};
                } else {
                    $obj->value = $item->value;
                }
                if ($lang == "ms"){
                    $obj->value = $item->value_ms ?? $obj->value;
                }
                if ($lang == "zh"){
                    $obj->value = $item->value_zh ?? $obj->value;
                }
                $obj->name = $item->{'name_' . $lang};
                $obj->type = $item->type;
                $obj->image = $item->image;
                $obj->color = $item->color;
                return $obj;
            })->toArray();
            $globalData = $this->getCoronaGlobal(
                $tableName,
                $lastDates[0]->stats_date,
                $lastDates[1]->stats_date ?? '2000-01-30'
            );

            $globalData["char_id"] = $globalChartId;

            //malaysia get 2nd - 3rd day, because when MOH give us data, it is actually data +24hours from 2 days ago
            $malaysiaFirstDayDate = Carbon::parse($lastDates[0]->stats_date)->subDays(1)->format('Y-m-d');
            $malaysiaSecondDayDate = Carbon::parse($lastDates[0]->stats_date)->subDays(2)->format('Y-m-d');

            $malaysiaData = $this->getMalaysiaCorona(
                $tableName,
                $malaysiaFirstDayDate,
                $malaysiaSecondDayDate ?? '2000-01-30',
                $lang
            );

            $malaysiaData["char_id"] = $malaysiaChartId;

            $result = [
                "updatedDate" => $globalData["updatedDate"],
                "malaysiaChartId" => $malaysiaChartId,
                "globalChartId" => $globalChartId,
                "shares" => $shares,
                "actions" => $actions,
                "countries" => [
                    $globalData,
                    $malaysiaData,
                ]
            ];
            return $result;
        }, 3);
    }

    private function getMalaysiaCorona($tableName, $latestDate, $latestDate2, $lang) {
        try {
            $select = DB::raw('id, stats_date, country, city, confirmed, suspected, death, cured');

            $malaysiaData = DB::table($tableName)
            ->whereNull('city')
            ->where(function($query) use ($latestDate, $latestDate2){
                $query->where('stats_date', $latestDate)
                ->orWhere('stats_date', $latestDate2);
            })
            ->where('country', 'Malaysia')
            ->select($select)
            ->get()
            ->keyBy('stats_date');

            $day1Data = $malaysiaData->get($latestDate);
            $day2Data = $malaysiaData->get($latestDate2);

            $day1MalaysiaData = DB::table("corona_malaysia_state_stats")->where('stats_date', $latestDate)
                ->select($select)
                ->get()
                ->keyBy('city');

            $day2MalaysiaData = DB::table("corona_malaysia_state_stats")->where('stats_date', $latestDate2)
                ->select($select)
                ->get()
                ->keyBy('city');

            $totalStates = ($day1MalaysiaData->count() - 5) . '';

            $top5States = $day1MalaysiaData
                ->sortByDesc('confirmed')
                ->map(function ($item) use ($day2MalaysiaData) {
                    $x = new \StdClass;
                    $x->name =  $item->city;
                    $x->confirmed =  $item->confirmed;
                    $x->death =  $this->decorateNumber($item->death) . "";
                    // $x->cured =  $this->decorateNumber($item->cured) . "";

                    $new = $item->confirmed - $day2MalaysiaData->get($item->city)->confirmed;
                    $x->new = $new;
                    return $x;
                })
                ->values()
                ->sortByMulti([
                    'new' => 'DESC',
                    'confirmed' => 'DESC'
                ])
                ->take(5)
                ->map(function ($item) {
                    $item->confirmed =  $this->decorateNumber($item->confirmed) . "";
                    $item->new = ( $item->new) > 0 ? $this->decorateNumber2( $item->new) : "0";
                    return $item;
                })
                ->all();
            $statesBreakdownUrl = "https://covid19.place/";
            if ($lang == "ms"){
                $statesBreakdownUrl = "https://covid19.place/?lang=ms-MY";
            }
            if ($lang == "zh"){
                $statesBreakdownUrl = "https://covid19.place/?lang=zh-CN";
            }
            return [
                "name" => "Malaysia",
                "new_confirmed" => $this->decorateNumber2(
                    $day1Data->confirmed - ($day2Data->confirmed ?? 0)  > 0 ? $day1Data->confirmed - ($day2Data->confirmed ?? 0) : 0),
                "total_confirmed" => $this->decorateNumber2($day1Data->confirmed),
                "total_deaths" =>  $this->decorateNumber2($day1Data->death),
                "new_deaths" => $this->decorateNumber2($day1Data->death - ($day2Data->death ?? 0) > 0 ? $day1Data->death - ($day2Data->death ?? 0) : 0),
                "total_cured" =>  $this->decorateNumber2($day1Data->cured),
                "new_cured" =>  $this->decorateNumber2($day1Data->cured - ($day2Data->cured ?? 0) > 0 ? $day1Data->cured - ($day2Data->cured ?? 0) : 0),
                "top5Countries" => $top5States,
                "states_breakdown_url" => $statesBreakdownUrl,
                "totalCountries" => $totalStates,
            ];
        } catch(\Exception $e) {
            notify_now($e);
            // return 0 for the numbers.
            // the objective is to not causing the app crash
            return [
                "name" => "Malaysia",
                "new_confirmed" => "0",
                "total_confirmed" => "0",
                "total_deaths" =>  "0",
                "new_deaths" => "0",
                "total_cured" =>  "0",
                "new_cured" =>  "0",
                "top5Countries" => $this->getTop5CountriesPlaceholder(),
                "states_breakdown_url" => $statesBreakdownUrl,
                "totalCountries" => "13",
            ];
        }
    }

    private function getCoronaGlobal($tableName, $latestDate, $latestDate2) {
        try {
            $select = DB::raw('id, stats_date, trim(country) as country, city, confirmed, suspected, death, cured, active, bid, updated_from_source');

            $day1Data = DB::table($tableName)->whereNull('city')->where('stats_date', $latestDate)
                ->select($select)
                ->get()
                ->keyBy('country');

            $day2Data = DB::table($tableName)->whereNull('city')->where('stats_date', $latestDate2)
                ->select($select)
                ->get()
                ->keyBy('country');

            $top5Countries  = $day1Data
            ->sortByDesc('confirmed')
            ->take(5)
            ->map(function ($item) use ($day2Data) {
                $x = new \StdClass;
                $x->name =  $item->country;
                $x->confirmed =  $this->decorateNumber($item->confirmed) . "";
                $x->death =  $this->decorateNumber($item->death) . "";
                $x->cured =  $this->decorateNumber($item->cured) . "";
                if (isset($day2Data->get($item->country)->confirmed)){
                    $new = $item->confirmed - $day2Data->get($item->country)->confirmed;
                }else{
                    $new = 0;
                }
                $x->new = ($new) > 0 ? $this->decorateNumber($new) : "0";
                return $x;
            })
            ->values()
            ->all();

            $totalCountries = ($day1Data->count() - 5) . '';

            return [
                "name" => "Global",
                "new_confirmed" => $this->decorateNumber($day1Data->sum('confirmed') - ($day2Data->sum('confirmed') ?? 0)  > 0 ? $day1Data->sum('confirmed') - ($day2Data->sum('confirmed') ?? 0) : 0),
                "total_confirmed" => $this->decorateNumber($day1Data->sum('confirmed')),
                "total_deaths" =>  $this->decorateNumber($day1Data->sum('death')),
                "new_deaths" => $this->decorateNumber($day1Data->sum('death') - ($day2Data->sum('death') ?? 0) > 0 ? $day1Data->sum('death') - ($day2Data->sum('death') ?? 0) : 0),
                "total_cured" =>  $this->decorateNumber($day1Data->sum('cured')),
                "new_cured" =>  $this->decorateNumber($day1Data->sum('cured') - ($day2Data->sum('cured') ?? 0) > 0 ? $day1Data->sum('cured') - ($day2Data->sum('cured') ?? 0) : 0),
                "top5Countries" => $top5Countries,
                "totalCountries" => $totalCountries,
                "updatedDate" => $day1Data->get('China')->updated_from_source,
            ];
        } catch(\Exception $e) {
            notify_now($e);
            // return 0 for the numbers.
            // the objective is to not causing the app crash
            return [
                "name" => "Global",
                "new_confirmed" => "0",
                "total_confirmed" => "0",
                "total_deaths" =>  "0",
                "new_deaths" => "0",
                "total_cured" =>  "0",
                "new_cured" =>  "0,",
                "top5Countries" => $this->getTop5CountriesPlaceholder(),
                "totalCountries" => "218",
                "updatedDate" => time(),
            ];
        }
    }

    private function getTop5CountriesPlaceholder() {
        return [
            [
                "name" => "NA",
                "confirmed" => "0",
                "death" => "0",
                "cured" => "0",
                "new" => "0"
            ],
            [
                "name" => "NA",
                "confirmed" => "0",
                "death" => "0",
                "cured" => "0",
                "new" => "0"
            ],
            [
                "name" => "NA",
                "confirmed" => "0",
                "death" => "0",
                "cured" => "0",
                "new" => "0"
            ],
            [
                "name" => "NA",
                "confirmed" => "0",
                "death" => "0",
                "cured" => "0",
                "new" => "0"
            ],
            [
                "name" => "NA",
                "confirmed" => "0",
                "death" => "0",
                "cured" => "0",
                "new" => "0"
            ]
        ];
    }

    private function getMerdekaData()
    {
        return cache("merdeka_data_2", function () {
            $data = [];
            $data['campaigns'] = DB::table('merdeka_2020_sponsor')->select('name', 'image_url', 'link')->orderBy('position')->get();
            $data['top10Videos'] = DB::table('merdeka_2020_video')->select('youtube_link', 'image_url', 'title')->orderBy('position')->get();
            return $data;
        }, 10);
    }

    private function decorateNumber($num)
    {

        if ($num > 1000) {
            $x = round($num);
            $x_number_format = number_format($x);
            $x_array = explode(',', $x_number_format);
            $x_parts = array('K', 'M', 'B', 'T');
            $x_count_parts = count($x_array) - 1;
            $x_display = $x;
            //  $x_display = $x_array[0] . ((int) $x_array[1][0] !== 0 ? '.' . $x_array[1][0].($x_array[1][1] ?? '') : '');
            $x_display = $x_array[0]; //. ((int) $x_array[1][0] !== 0 ? '.' . $x_array[1][0]. : '');
            $x_display .= $x_parts[$x_count_parts - 1];
            return $x_display;
        }

        return $num . "";
    }

    private function decorateNumber2($num)
    {
        return number_format($num) . "";
    }

    private function getParliamentStats($lang)
    {

        $rep = app(ParliamentRepository::class);
        return [
            "updatedDate" => $rep->getUpdatedDate(),
            "parties" => $rep->getPartiesData()->values()
        ];
    }
    private function translate($word, $lang)
    {
        if ($lang == "en" || $lang == "ms") return $word;
        else if ($lang == "zh") {
            switch ($word) {
                case "Confirm":
                    return "确诊人数";
                case "Suspect":
                    return "疑似病例";
                case "Death":
                    return "死亡人数";
                case "Cured":
                    return "病愈人数";
            }
        }
        return $word;
    }

    private function getExcludedArticles($profileId, $days = 10)
    {
        $articles = cache_feed_get($profileId);
        try {
            $data = DB::connection('track_cluster')
                ->table("user_article_stats")
                ->select('article_id')
                ->where('user_id', $profileId)
                ->whereNotIn('article_id', $articles)
                ->where('updated_at', '>=', Carbon::now()->subDays($days)->timestamp)
                ->orderByDesc('updated_at')
                ->limit(300)
                ->pluck('article_id');
            foreach ($data as $article) {
                $articles[] = $article;
            }
            return $articles;
        } catch (\Exception $e) {
            notify_now($e);
        }
        return [];
    }


    private function mergeArticlesWithUserArticleStats(&$articles, $userId)
    {

        $userStats = collect();
        if (Str::startsWith($userId, 'i_') ||  Str::startsWith($userId, 'a_')) {
            try {
                $userStats = DB::connection('track_cluster')
                    ->table('user_article_stats')
                    ->select('article_id', 'views', 'feed')
                    ->whereIn('article_id', $articles->pluck('articleId')->all())
                    ->where('user_id', $userId)
                    ->where('updated_at', '>=', Carbon::now()->subMinutes(30))
                    ->limit(500)
                    ->get()
                    ->keyBy('article_id');
            } catch (\Exception $e) {
                notify_now($e);
            }
        }
        return $articles->map(function ($item) use ($userStats) {
            $item->userViews = $userStats->get($item->articleId)->views ?? "0";
            $item->userFeed = $userStats->get($item->articleId)->feed ?? "0";
            return $item;
        });
    }

    private function time_elapsed_string($datetime, $now = false, $full = false)
    {
        $now = $now ? $now : new \DateTime;
        $ago = new \DateTime($datetime);
        $diff = $now->diff($ago);

        $diff->w = floor($diff->d / 7);
        $diff->d -= $diff->w * 7;

        $string = array(
            'y' => 'y',
            'm' => 'M',
            'w' => 'w',
            'd' => 'd',
            'h' => 'h',
            'i' => 'm',
            's' => 's',
        );
        foreach ($string as $k => &$v) {
            if ($diff->$k) {
                $v = $diff->$k . '' . $v . ($diff->$k > 1 ? '' : '');
            } else {
                unset($string[$k]);
            }
        }

        if (!$full) $string = array_slice($string, 0, 1);
        return $string ? implode(', ', $string)  : 'just now';
    }

    private function humanize_number($num)
    {
        if ($num > 1000) {

            $x = round($num);
            $x_number_format = number_format($x);
            $x_array = explode(',', $x_number_format);
            $x_parts = array('k', 'm', 'b', 't');
            $x_count_parts = count($x_array) - 1;
            $x_display = $x;
            $x_display = $x_array[0] . ((int)$x_array[1][0] !== 0 ? '.' . $x_array[1][0] : '');
            $x_display .= $x_parts[$x_count_parts - 1];

            return $x_display;
        }

        return $num;
    }

    private function get_top_reaction($item){
        $reactions = [
            'happy' => (int)$item->happy ?? 0,
            'cry' => (int)$item->cry ?? 0,
            'shocked' => (int)$item->shocked ?? 0,
            'laugh' => (int)$item->laugh ?? 0,
            'meh' => (int)$item->meh ?? 0,
            'angry' => (int)$item->angry ?? 0,
        ];

        $total_reaction = 0;
        $max = $max2 = 0;
        $first_reaction = "happy";
        $second_reaction = "shocked";
        foreach ($reactions as $key => $value) {
            $total_reaction = $total_reaction + $value;
            if ($value >= $max && $value != 0) {
                $max2 = $max;
                $max = $value;
                $second_reaction = $first_reaction;
                $first_reaction = $key;
            } else if ($value >= $max2 && $value != $max && $value != 0) {
                $max2 = $value;
                $second_reaction = $key;
            }
        }
        if ($second_reaction == $first_reaction && $second_reaction == "happy") {
            $second_reaction = "shocked";
        }

        return collect(['first_reaction' => $first_reaction, 'second_reaction' => $second_reaction]);
    }

    public function getBookmarkedArticles(?array $ids):array{
        $data =  $this->getArticles($ids);
        // return $data->map(function ($d){
        //     $d->unique_id = $d->uniqueID;
        // });

        return array_map(function ($d){
            $d->unique_id = $d->uniqueID;
            return $d;
        }, $data);
    }

    private function getUserBookmarkData($ids, $firebase_id){
        $show_bookmark = config('app.bookmark_flag');
        if (!$show_bookmark){
            return [];
        }
        $repo = app(BookmarkRepository::class);
        return $repo->getUserBookmarkDataByIds($ids, "a", $firebase_id);
    }

    private function getUserHistoryData($ids, $firebase_id){
        $show_history = config('app.history_flag');
        if (!$show_history){
            return [];
        }
        $repo = app(HistoryRepository::class);
        return $repo->getUserHistoryDataByIds($ids, "a", $firebase_id);

    }

    public function getArticlesPublishersOrTopics(
        $hours,
        $native
    ) {

        $now = Carbon::now();
        $past = Carbon::now()->subHour((int)$hours);
        $query = DB::table('predictions')
            ->select('topic', 'publisher_id')
            ->distinct()
            ->whereBetween('published_date', [$past, $now])
            ->where('topic', '!=', -1);
        $disabledChannels = getDisabledChannelsFromCache();
        $channel = $this->getChannels($native, $disabledChannels);
        if ($channel) {
            // $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
            $query = $query->whereIn('channel_id', $channel);
        }


        return $query;

    }

    private function getChannels($native, $disabled){
        $query =  DB::table('channels')
        ->select('id')
        ->whereNotIn('id', explode(',', $disabled));
        if(!is_null($native)){
            $query->where('reader_view_only', (int)$native);
        }
        return $query->get()->pluck('id');
    }

    public function getContentArticles(
        $hours, $publishers_id, $topics_id, $language, $native
    ) {

        $now = Carbon::now();
        $past = Carbon::now()->subHour($hours);
        $query = DB::table('predictions')
            ->selectRaw("unique_id, max(published_date) published_date, max(title) title, max(description) description, max(language) language, max(publisher_id) publisher_id, GROUP_CONCAT(topic) topic, max(view_count) view_count, max(comment_count) comment_count, max(reaction_count) reaction_count")
            ->whereBetween('published_date', [$past, $now]);
        $disabledChannels = getDisabledChannelsFromCache();
        $channel = $this->getChannels($native, $disabledChannels);
        if ($channel) {
            // $query = $query->whereNotIn('channel_id', \explode(',', $disabledChannels));
            $query = $query->whereIn('channel_id', $channel);
        }
        if (count($publishers_id) > 0){
            $query->whereIn('publisher_id', $publishers_id);
        }
        if (count($topics_id) > 0){
            $query->whereIn('topic', $topics_id);
        }
        if (count($language) > 0){
            $query->whereIn('language', $language);
        }

        $data = $query->groupBy('unique_id')->orderByDesc('published_date')->get();
        $p = array_values($data->pluck('publisher_id')->toArray());
        $t = array_values($data->pluck('topic')->toArray());
        $a_ids = array_values($data->pluck('unique_id')->toArray());


        $stats = DB::table('article_stats')
            ->select('shares',"article_id")
            ->whereIn('article_id', $a_ids)
            ->get()
            ->keyBy('article_id');


        $topicsRepo = app(TopicRepository::class);
        $publisherRepo = app(PublisherRepository::class);

        $permalinks = Article::select('uniqueID', 'permalink')
        ->whereIn('uniqueID', $a_ids)
        ->get()
        ->keyBy('uniqueID');

        $translations = ArticleContent::select('_id', 'unique_id')->whereIn('unique_id', $a_ids)->get();

        $p = $publisherRepo->getPublishers($p);
        $t = $topicsRepo->getTopics($t);

        $data = $data->map(function($item) use(&$p, &$t, &$stats, &$permalinks, $translations){
            $item->publisher = $p->where('id', $item->publisher_id)->first()->publisherName ?? "";
            $topics = [];
            foreach (explode(",",$item->topic) as $tid){
                $topics[] = $t->where('id', $tid)->first()->nameEN ?? "-" ;
            }
            $item->topic = implode(",", array_unique($topics));
            $item->share_count = $stats->get($item->unique_id)->shares ?? 0;
            $item->content_type = "a";
            $permalink = $permalinks->get($item->unique_id)->permalink ?? $item->unique_id;
            $item->link = "article/" . $permalink;
            $item->is_translated = $translations->where('unique_id', $item->unique_id)->count() > 0;
            return $item;
        });

        return $data;

    }



    private function getImportantPublisherIds(){
        return cache("nw_important_publishers", function(){
            // get wavmaker and headliner publisher_id
            $publisherIds = DB::table('publishers')
            ->select(DB::raw('id'))
            ->whereIn('project', ['headliner', 'wavmaker', 'newswav','ugc'])
            ->pluck('id')
            ->toArray();

        // merge with important publishers
        return array_unique(array_merge($this->importantPublishers, $publisherIds));
        }, 5);
    }


    /**
     * > Get all the disabled contents from the database where the unique id is in the array of unique
     * ids
     *
     * @param uniqueIds An array of unique ids of the contents you want to get.
     *
     * @return A collection of disabled content objects.
     */
    private function getDisabledContents($uniqueIds){
        return DB::table('disabled_contents')->whereIn('unique_id', $uniqueIds)->get()->keyBy('unique_id');
    }



}


// function paginateWithoutKey($items, $perPage = 10, $page = null, $options = [])
//     {

//         $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);

//         $items = $items instanceof Collection ? $items : Collection::make($items);

//         $lap = new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);

//         return [
//             'current_page' => $lap->currentPage(),
//             'data' => $lap->values(),
//             'first_page_url' => $lap->url(1),
//             'from' => $lap->firstItem(),
//             'last_page' => $lap->lastPage(),
//             'last_page_url' => $lap->url($lap->lastPage()),
//             'next_page_url' => $lap->nextPageUrl(),
//             'per_page' => $lap->perPage(),
//             'prev_page_url' => $lap->previousPageUrl(),
//             'to' => $lap->lastItem(),
//             'total' => $lap->total(),
//         ];
//     }


