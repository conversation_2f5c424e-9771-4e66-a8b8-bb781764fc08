<?php

namespace App\V4\Repositories;

use App\V4\Common\Constants;
use App\V4\Models\Article;
use Carbon\Carbon;

class ArticleRepository extends BaseRepository
{
    public function __construct(Article $model)
    {
        parent::__construct($model);
    }

    public function getMostViewedArticles(string $fromDate, string $toDate, ?array $languages, ?string $uniqueId, ?array $projects, int $page = 1): array
    {
        $fromDateTime = Carbon::parse($fromDate)->startOfDay();
        $toDateTime = Carbon::parse($toDate)->endOfDay();

        $paginatedResults = $this->model
            ->select(
                'article.uniqueID as unique_id',
                'article.title',
                'article.viewCount as views',
                'article.reactionCount as reactions',
                'article.commentCount as comments',
                'article_stats.shares',
                'publishers.name as publisher',
                'publishers.project as project'
            )
            ->join('channels', 'article.channelID', '=', 'channels.id')
            ->join('publishers', 'channels.publisher_id', '=', 'publishers.id')
            ->leftJoin('article_stats', 'article.uniqueID', '=', 'article_stats.article_id')
            ->when($languages, fn($q) => $q->whereIn('article.language', $languages))
            ->when($uniqueId, fn($q) => $q->where('article.uniqueID', $uniqueId))
            ->when($projects, fn($q) => $q->whereIn('publishers.project', $projects))
            ->whereBetween('article.publishedDate', [$fromDateTime, $toDateTime])
            ->orderByDesc('article_stats.views')
            ->paginate(Constants::DEFAULT_ITEMS_PER_PAGE);


        return [
            'data' => $paginatedResults->items(),
            'pagination' => [
                'total_pages' => $paginatedResults->lastPage(),
                'current_page' => $paginatedResults->currentPage(),
                'per_page' => $paginatedResults->perPage(),
                'next_page_url' => $paginatedResults->nextPageUrl(),
                'prev_page_url' => $paginatedResults->previousPageUrl(),
                'has_more_pages' => $paginatedResults->hasMorePages(),
            ]
        ];
    }
}
