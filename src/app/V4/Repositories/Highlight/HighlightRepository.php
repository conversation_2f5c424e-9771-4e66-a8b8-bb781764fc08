<?php

namespace App\V4\Repositories\Highlight;

use App\Repositories\Publisher\PublisherRepository;
use App\V4\Exceptions\HighlightInactiveExceptionV4;
use App\V4\Exceptions\HighlightNotFoundExceptionV4;
use App\V4\Exceptions\HighlightTranslationNotFoundExceptionV4;
use Carbon\Carbon;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Lumen\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\Newswav\UserProfile\UserProfileService;
use App\Services\Newswav\Pin\PinItemService;
use App\Models\FbUser;
use App\Models\NwUser;
use App\V4\Models\NwUser as ModelsNwUser;
use App\V4\Models\NwWebUser;

class HighlightRepository
{
    public function getActiveHighlightsByFeedId($feedId, $mainLang, $languages){
        $carbon = Carbon::now();

        $highlights = DB::table('highlights')
                            ->select('highlights.id', 'highlights.slug', 'highlights.imageUrl', 'highlights_feed.feedId',
                                'highlights.startDate', 'highlights.endDate', 'highlights.type', 'highlights.isPinned', 'highlights.isCommentable')
                            ->join('highlights_feed', 'highlights.id', '=', 'highlights_feed.highlightId')
                            ->where(function($q) use($feedId){
                                $q->where('highlights_feed.feedId', $feedId)
                                ->orWhere('highlights_feed.feedId', 'F_F_ALL');
                            })
                            ->where('startDate', '<=', $carbon->format('Y-m-d H:i:s'))
                            ->where('endDate', '>=', $carbon->format('Y-m-d H:i:s'))
                            ->get();

        if (count($highlights) == 0) return collect([]);

        $highlightIds = $highlights->pluck('id')->toArray();
        //get translation
        $highlightsTranslation = DB::table('highlights_translation')
                                    ->select('id','highlightId', 'language', 'title', 'description')
                                    ->whereIn('highlightId', $highlightIds)
                                    ->whereIn('language', array_merge($languages,[$mainLang]))
                                    ->get();
        // rewrite available languages, this will affect $latestContents filter
        $languages = array_intersect(array_merge($languages,[$mainLang]), $highlightsTranslation->pluck('language')->toArray());

        $highlightIdsOfTypeContents = $highlights->where('type', '=', 'contents')->pluck('id')->toArray();
        $highlightsByContents = DB::table('highlight_contents')->whereIn('highlightId', $highlightIdsOfTypeContents)->select('highlightId', 'contentId')->get();
        $contentIdToHighlightIdMap = (object)[];
        foreach($highlightsByContents as $highlightContent) {
            $contentIdToHighlightIdMap->{$highlightContent->contentId} = $highlightContent->highlightId;
        }
        $postsByContentIds = $this->highlightByContents($highlightsByContents->pluck('contentId')->toArray(), $languages, $contentIdToHighlightIdMap);


        $highlightIdsOfTypePublishers = $highlights->where('type', '=', 'publishers')->pluck('id')->toArray();
        $highlightsByPublishers = DB::table('highlight_publishers')->whereIn('highlightId', $highlightIdsOfTypePublishers)->select('highlightId', 'publisherId')->get();
        $publisherIdToHighlightIdMap = (object)[];
        foreach($highlightsByPublishers as $highlightContent) {
            $publisherIdToHighlightIdMap->{$highlightContent->publisherId} = $highlightContent->highlightId;
        }
        $postsByPublisherIds = $this->highlightByPublishers($highlightsByPublishers->pluck('publisherId')->toArray(), $languages, $publisherIdToHighlightIdMap);

        dd($highlightIds);
        $postsByKeywords = DB::table('highlight_content_mapping')
        ->selectRaw('highlightId, max(publishedDate) as publishedDate')
        ->whereIn('highlightId', $highlightIds)
        ->where('publishedDate', '<=', Carbon::now()->timestamp)
        ->whereIn('language', $languages)
        ->groupBy('highlightId')
        ->get();

        $latestContents = [];
        foreach($postsByKeywords as $keywordsPost) {
            $latestContents[] = (object)[
                'highlightId' => $keywordsPost->highlightId,
                'publishedDate' => $keywordsPost->publishedDate,
                'type' => 'keywords',
            ];
        }
        foreach($postsByContentIds as $contentIdPost) {
            $obj = (object)$contentIdPost;
            if (!count($obj->contentIds)) continue;
            $latestContents[] = (object)[
                'highlightId' => $obj->highlightId,
                'publishedDate' => $obj->publishedDate,
                'type' => 'contents',
                'contentIds' => $obj->contentIds,
            ];
        }
        foreach($postsByPublisherIds as $publisherIdPost) {
            $obj = (object)$publisherIdPost;
            if (!count($obj->publisherIds)) continue;
            $latestContents[] = (object)[
                'highlightId' => $obj->highlightId,
                'publishedDate' => $obj->publishedDate,
                'type' => 'publishers',
                'publisherIds' => $obj->publisherIds,
            ];
        }
        $latestContents = collect($latestContents);

        return array_values($highlights->map(function($item) use ($highlightsTranslation, $latestContents, $mainLang, $languages){
            $item->isPinned = (bool)$item->isPinned;
            $item->isCommentable = (bool)$item->isCommentable;

            $translationsByHighlightId = $highlightsTranslation->where('highlightId', $item->id);
            $translation = $translationsByHighlightId->where('language', $mainLang)->first() ?: $translationsByHighlightId->first();

            if ($translation){
                $item->meta = (object)[
                    'highlight' => $translation
                ];
                $latest = $latestContents->where('highlightId', $item->id)->first();
                if ($latest) { //only return highlight with contents. no latest publishedDate = no content
                    $item->latest = intval($latest->publishedDate);
                    return $item;
                }
            }
            return null;
        })->filter(function ($value) { return !is_null($value); })->sortbyDesc('latest')->sortByDesc('isPinned')->values()->toArray());

    }

    /** @return array highlights by specified content ids and its max published date */
    public function highlightByContents($contentIds, $languages, $mapToHighlightId) {
        $articles = DB::table('predictions')
            ->whereIn('unique_id', $contentIds)
            ->where('published_date', '<=', Carbon::now())
            ->whereIn('language', $languages)
            ->select('unique_id')
            ->selectRaw('UNIX_TIMESTAMP(published_date) AS published_date')
            ->get();
        $videos = DB::table('video_predictions')
            ->whereIn('unique_id', $contentIds)
            ->where('published_date', '<=', Carbon::now()->timestamp)
            ->whereIn('language', $languages)
            ->select('unique_id', 'published_date')
            ->get();
        $merged = $articles->merge($videos)->transform(function($item) use ($mapToHighlightId) {
            return [
                'uniqueId' => $item->unique_id,
                'publishedDate' => $item->published_date,
                'highlightId' => $mapToHighlightId->{$item->unique_id},
            ];
        });
        return $merged->groupBy('highlightId')->transform(function($item) {
            return [
                'highlightId' => $item->pluck('highlightId')->first(),
                'contentIds' => $item->pluck('uniqueId')->toArray(),
                'publishedDate' => max($item->pluck('publishedDate')->toArray())
            ];
        });
    }

    /** @return array highlights by specified publisher ids and its max published date */
    public function highlightByPublishers($publisherIds, $languages, $mapToHighlightId) {
        $articles = DB::table('predictions')
            ->whereIn('publisher_id', $publisherIds)
            ->where('published_date', '<=', Carbon::now())
            ->whereIn('language', $languages)
            ->select('publisher_id')
            ->selectRaw('UNIX_TIMESTAMP(published_date) AS published_date')
            ->get();
        $videos = DB::table('video_predictions')
            ->whereIn('publisher_id', $publisherIds)
            ->where('published_date', '<=', Carbon::now()->timestamp)
            ->whereIn('language', $languages)
            ->select('publisher_id', 'published_date')
            ->get();
        $merged = $articles->merge($videos)->transform(function($item) use ($mapToHighlightId) {
            return [
                'publisherId' => $item->publisher_id,
                'publishedDate' => $item->published_date,
                'highlightId' => $mapToHighlightId->{$item->publisher_id},
            ];
        });
        return $merged->groupBy('highlightId')->transform(function($item) {
            return [
                'highlightId' => $item->pluck('highlightId')->first(),
                'publisherIds' => $item->pluck('publisherId')->toArray(),
                'publishedDate' => max($item->pluck('publishedDate')->toArray())
            ];
        });
    }

    public static function getSurrogateUnionTable() {
        return DB::raw('(SELECT 1 AS _id) AS _INNER');
    }

    /** Returns unique ids and published date by highlight type publishers */
    public function uniqueIdsByPublishers($publisherIds, $contentTypes, $languages, $publishedDate, $backtrackLastOfDays = 7, $hiddenContents = []) {
        $innerQuery = DB::table(DB::raw($this->getSurrogateUnionTable()))
        ->selectRaw('NULL AS uniqueId')
        ->selectRaw('NULL AS language')
        ->selectRaw('NULL AS contentType')
        ->selectRaw('NULL AS publishedDate')
        ->whereNull('_id');

        if (in_array('v', $contentTypes)) {
            $innerQuery = $innerQuery->union(
                DB::table('video_predictions')
                    ->select([
                        'video_predictions.unique_id AS uniqueId',
                        'video_predictions.language',
                    ])
                    ->selectRaw("'v' as contentType")
                    ->selectRaw("published_date")
                    ->whereIn('video_predictions.publisher_id', $publisherIds)
                    ->whereIn('video_predictions.language', $languages)
                    ->whereRaw('published_date < ?', [$publishedDate])
                    ->whereRaw('published_date >= ?', [$publishedDate - ($backtrackLastOfDays * 86400)])
                    ->when(count($hiddenContents) > 0, function ($query) use ($hiddenContents) {
                        $query->whereNotIn('video_predictions.unique_id', $hiddenContents);
                    })

                );

        }

        if (in_array('a', $contentTypes)) {
            $innerQuery = $innerQuery->union(
                DB::table('predictions')
                ->select([
                    'predictions.unique_id AS uniqueId',
                    'predictions.language AS language',
                ])
                ->selectRaw("'a' as contentType")
                ->selectRaw("UNIX_TIMESTAMP(published_date) as publishedDate")
                ->whereIn('predictions.publisher_id', $publisherIds)
                ->whereIn('predictions.language', $languages)
                ->whereRaw('published_date < FROM_UNIXTIME(?)', [$publishedDate])
                ->whereRaw('published_date >= FROM_UNIXTIME(?)', [$publishedDate - ($backtrackLastOfDays * 86400)])
                ->when(count($hiddenContents) > 0, function ($query) use ($hiddenContents) {
                    $query->whereNotIn('predictions.unique_id', $hiddenContents);
                })
            );
        }

        return $innerQuery->orderByDesc('publishedDate');
    }

    public function uniqueIdsByLanguages($highlightId, $languages, $last = null, $hiddenContents = [], $hiddenPublishers = []) {
        $now = Carbon::now();
        $innerQuery = DB::table(self::getSurrogateUnionTable())
        ->selectRaw('null AS uniqueId')
        ->selectRaw('null AS publishedDate')
        ->whereNull('_id')
        ->union(
            DB::table('predictions')
            ->select([
                'predictions.unique_id',
            ])
            ->selectRaw('UNIX_TIMESTAMP(predictions.published_date)')
            ->leftJoin('highlight_contents', 'highlight_contents.contentId', '=', 'predictions.unique_id')
            ->where('highlightId', $highlightId)
            ->whereIn('language', $languages)
            ->where('published_date', '<=', $now)
            ->when(!is_null($last), function($q) use ($last) {
                return $q->where('published_date', '<', Carbon::createFromTimestamp($last));
            })
            ->when(count($hiddenContents) > 0, function ($query) use ($hiddenContents) {
                $query->whereNotIn('predictions.unique_id', $hiddenContents);
            })
            ->when(!empty($hiddenPublishers), function ($query) use ($hiddenPublishers){
                $query->whereNotIn('publisher_id', $hiddenPublishers);
            })
        )
        ->union(
            DB::table('video_predictions')
            ->select([
                'video_predictions.unique_id',
                'video_predictions.published_date'
            ])
            ->leftJoin('highlight_contents', 'highlight_contents.contentId', '=', 'video_predictions.unique_id')
            ->where('highlightId', $highlightId)
            ->whereIn('language', $languages)
            ->where('published_date', '<=', $now->timestamp)
            ->when(!is_null($last), function($q) use ($last) {
                return $q->where('published_date', '<', $last);
            })
            ->when(count($hiddenContents) > 0, function ($query) use ($hiddenContents) {
                $query->whereNotIn('video_predictions.unique_id', $hiddenContents);
            })
            ->when(!empty($hiddenPublishers), function ($query) use ($hiddenPublishers){
                $query->whereNotIn('publisher_id', $hiddenPublishers);
            })
        )
        ->orderByDesc('publishedDate');
        return $innerQuery;
    }

    /**
     * > Get the contents of a highlight by its slug, languages, content type, page, limit, last, and
     * feed id
     *
     * @param highlightSlug the slug of the highlight
     * @param languages array of languages
     * @param contentType array of content types (e.g. ['article', 'video'])
     * @param page the page number of the pagination
     * @param int limit the number of items to return
     * @param last the last published date of the last item in the previous page
     * @param feedId the feedId of the user. If not provided, the current user's feedId will be used.
     */
    public function getHighlightContents(
        $highlightSlug,
        $languages,
        $contentType,
        $page,
        int $limit = 30,
        $last,
        $feedId = null
    ) {
        $emptyContent = (object)[
            'data' => [],
            'next_page_url' => '',
            'next_page' => '',
            'last' => null,
        ];

        $highlightIdAndType = $this->getHighlightIdAndTypeBySlug($highlightSlug);
        $highlightId = $highlightIdAndType->id;
        $highlightType = $highlightIdAndType->type;
        // overwrite available highlight languages
        $languages = array_intersect($languages, $highlightIdAndType->languages);
        if (!sizeof($languages)) {
            // detection order for languages should be as follows: EN > MS > ZH
            sort($highlightIdAndType->languages);
            $languages = [$highlightIdAndType->languages[0]];
        }
        /**
         * user not interested contents
         * no join/subquery - Illegal mix of collations (utf8mb4_unicode_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='
         * will be using query -> pluck unique ids -> whereNotIn query
         */

        /** @var FbUser|NwUser|NwWebUser|ModelsNwUser $user */
        $user = Auth::user();
        $firebaseId = $user->getFirebaseId();
        /** @var UserProfileservice $userService */
        $userService = app(UserProfileService::class);
        $hiddenContents = $userService->getHiddenContentList($firebaseId);
        $hiddenPublishers = $userService->getMutedPublishersList($firebaseId);
        /**
         * 7/11/2022 update
         * pinned highlights - located in pinned table, type = highlight
         * fetch by highlight slug regardless of highlight type
         */
        /** @var PinItemService @pinService */
        $pinService = app(PinItemService::class);
        $pinnedItems = $pinService->getPinned($highlightSlug, ["highlight"], null);
        //exclude from query
        if ($pinnedItems > 0){
            $hiddenContents = array_merge($hiddenContents, $pinnedItems);
        }

        // paginations will be computed separately for highlights type publishers
        if ($highlightType === 'publishers') {
            $publisherIds = DB::table('highlight_publishers')
            ->where('highlightId', $highlightId)
            ->leftJoin('publishers', 'publishers.id', '=', 'highlight_publishers.publisherId')
            ->where('enabled', true)
            /** update https://newswav.atlassian.net/browse/NWGEN-564 */
            ->when(!empty($hiddenPublishers), function ($query) use ($hiddenPublishers){
                return $query->whereNotIn('highlight_publishers.publisherId', $hiddenPublishers);
            })
            ->pluck('publisherId')
            ->toArray();
            if (!count($publisherIds)){
                //in case the highlight is in different language, but we pin in different language, return the highlight content anyway
                if (count($pinnedItems) > 0){
                    $emptyContent->data = $pinnedItems;
                }
                return $emptyContent;
            }

            $backtrackLastOfDays = 7;
            $from = $last ?? Carbon::now()->timestamp;
            $query = $this->uniqueIdsByPublishers($publisherIds, $contentType, $languages, $from, $backtrackLastOfDays, $hiddenContents);
            if (!$query) {
                if (count($pinnedItems) > 0){
                    $emptyContent->data = $pinnedItems;
                }
                return $emptyContent;
            }
            // perform backtrack query at most five times
            // e.g.             2022-07-31 to 2022-07-24 (7 days)
            // then if less,    2022-07-24 to 2022-07-10 (14 days)
            // then if less,    2022-07-10 to 2022-06-12 (28 days)
            // and so on.. on the last iteration, if still less than
            // limit, the loop will just break
            $maxLoop = 4;
            $result = $query->take($limit)->get();
            $originalLimit = $limit;
            while($maxLoop && count($result) < $limit) {
                $from = $result->pluck('publishedDate')->last() ?? $from;
                $backtrackLastOfDays *= 2;
                $limit = $originalLimit - count($result);
                $query = $this->uniqueIdsByPublishers($publisherIds, $contentType, $languages, $from, $backtrackLastOfDays);
                $result = $result->merge($query->take($limit)->get());
                --$maxLoop;
            }

            if (count($result) < $limit) {
                $noNextResult = clone $emptyContent;
                $noNextResult->data = $result->pluck('uniqueId')->toArray();
                if (count($pinnedItems) > 0 && $page == 1){
                    $noNextResult->data = array_merge($pinnedItems, $noNextResult->data);
                }
                return $noNextResult;
            }
            $last = $result->pluck('publishedDate')->last() ?? 0;
            $q = app()->request->query->all();
            $q['last'] = $last;
            $pg = new Paginator([], 1, null, [
                'path' => Paginator::resolveCurrentPath(),
                'query' => $q,
            ]);
            return (object)[
                'data' => count($pinnedItems) > 0 && $page == 1 ? array_merge($pinnedItems, $result->pluck('uniqueId')->toArray()) : $result->pluck('uniqueId')->toArray(),
                'next_page_url' => $pg->url($page + 1),
                'next_page' => $page + 1,
                'last' => $last,
            ];
        }

        if ($highlightType === 'keywords') {
            // get user's muted publisher channel ids
            $disabledChannelIds = app(PublisherRepository::class)->getChannelIdsByPublisherIds($hiddenPublishers);
            $query = DB::table('highlight_content_mapping')
            ->select('highlight_content_mapping.uniqueId', 'publishedDate')
            ->leftJoin('disabled_contents', 'highlight_content_mapping.uniqueId', '=', 'disabled_contents.unique_id')
            ->distinct()
            ->where('highlightId', $highlightId)
            ->whereIn('language', $languages)
            ->whereIn('contentType', $contentType)
            ->where('publishedDate', '<=', Carbon::now()->timestamp)
            ->where('disabled', 0)
            ->when(!empty($disabledChannelIds), function ($query) use ($disabledChannelIds){
                $query->whereNotIn('channelId', $disabledChannelIds);
            })
            ->when($last, function ($query) use (&$last) {
                return $query->where('publishedDate', '<', $last);
            })
            ->when(count($hiddenContents) > 0, function ($query) use ($hiddenContents) {
                $query->whereNotIn('highlight_content_mapping.uniqueId', $hiddenContents);
            })
            ->whereNull('disabled_contents.unique_id')
            ->orderBy('publishedDate', 'DESC');
        } else if ($highlightType === 'contents') {
            $query = $this->uniqueIdsByLanguages($highlightId, $languages, $last, $hiddenContents, $hiddenPublishers);
        } else {
            Log::warning("Expecting highlight to have a type of keywords, contents, or publishers for ID $highlightId but got $highlightType instead. Returning empty contents...");
            $query = null;
        }

        if ($query === null) return $emptyContent;

        $ids = array();
        $articles = (object)[];
        $result = $query
            ->take($limit)
            ->get();
        $articles = (object)[];
        $articles->last = $result->pluck('publishedDate')->last() ?? 0;
        $articles->data = $result->map(function ($item) {
            return $item->uniqueId;
        })->toArray();
        if ($page == 1) {
            $articles->data = array_merge($pinnedItems, $articles->data);
        }
        // next page url
        $last = $articles->last;
        $q = app()->request->query->all();
        $q['last'] = $last;
        $pg = new Paginator([], 1, null, [
            'path' => Paginator::resolveCurrentPath(),
            'query' => $q,
        ]);

        $hasNext = count($result) == $limit;
        if ($hasNext) {
            $articles->next_page_url = $pg->url($page + 1);
            $articles->next_page = $page + 1;
        } else {
            $articles->next_page_url = "";
        }
        return $articles;
    }

    public function getHighlight($highlightSlug, $languages){
        if (!is_array($languages)) $languages = [$languages];
        $carbon = Carbon::now();
        $highlight = DB::table('highlights')
                        ->select('id', 'imageUrl', 'slug')
                        ->where('startDate', '<=', $carbon)
                        ->where('endDate', '>=', $carbon)
                        ->where('slug', $highlightSlug)->first();

        if (!$highlight){
            throw new HighlightNotFoundExceptionV4();
            //abort(404, "highlight not found");
        }
        $implodeLang = "'" . implode("','", $languages) . "'";
        $highlight_translated = DB::table('highlights_translation')
                                    ->select('language', 'title', 'description')
                                    ->where('highlightId', $highlight->id)
                                    ->where('language', $languages[0] ?? 'en')
                                    ->first();
        if (!$highlight_translated){
            throw new HighlightTranslationNotFoundExceptionV4();
            //abort(404, "no highlight translation");
        }

        $highlight->language = $highlight_translated->language;
        $highlight->title = $highlight_translated->title;
        $highlight->description = $highlight_translated->description;


        return $highlight;

    }

    private function getHighlightIdAndTypeBySlug($highlightSlug){
        $carbon = Carbon::now();
        $highlight = DB::table('highlights')->select('id', 'slug', 'type', 'startDate', 'endDate')->where('slug', $highlightSlug)->first();

        if (!$highlight){
            throw new HighlightNotFoundExceptionV4();
            //abort(404, "highlight not found");
        }
        if ($highlight->startDate >= $carbon || $highlight->endDate <= $carbon){
            throw new HighlightInactiveExceptionV4();
        }

        $languages = DB::table('highlights_translation')->where('highlightId', $highlight->id)->pluck('language')->toArray();

        return (object)[
            'id' => $highlight->id,
            'type' => $highlight->type,
            'languages' => $languages,
        ];
    }

}
