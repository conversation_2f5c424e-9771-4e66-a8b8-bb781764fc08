<?php

namespace App\V4\Services;

use App\Models\FbUser;
use App\Repositories\Article\ArticleRepository;
use App\Repositories\Video\VideoRepository;
use App\V4\Repositories\Content\ContentRepository;
use App\V4\Repositories\Highlight\HighlightRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

define("HIGHLIGHT_FEED_SIZE", 8);
class HighlightService
{
    protected  $user;

    public function getActiveHighlights($user, $feedId, $contentType, $web = false){
        $this->user = $user;
         /**  @var HighlightRepository $highlightRepo **/
        $highlightRepo = app(HighlightRepository::class);

        //languages for web and lite and new app version
        // $languages = $this->user->getLanguages();
        $mainLang = $this->user->getMainLanguage();
        if (!$this->checkLite() && !$web && !checkNewApp()){
            //need to override language for current app (use languages from header)
            $request = app('request');
            $token = $request->header('nwtoken');
            $this->user = new FbUser($request, $token);
            $languages = $this->user->getUserLanguages();
            $mainLang = $this->user->getMainLanguage();
        }

        if (!(like_match('F_F_%', $feedId) || like_match('F_T_%', $feedId) || like_match('F_P_%', $feedId))){
            $contentRepo = app(ContentRepository::class);
            $feedId = $contentRepo->getFeedBySlug($feedId)->id;
        }

        sort($languages);
        $key = "{$feedId}_{$mainLang}_".implode('_',$languages);
        return cache('adwav:highlight_active_'.$key, function() use ($highlightRepo, $feedId, $mainLang, $languages){
           return $highlightRepo->getActiveHighlightsByFeedId($feedId, $mainLang, $languages);
        },14);
    }

    public function getHighlight($user, $highlightSlug, $language, $web = false){
        $this->user = $user;
        /**  @var HighlightRepository $highlightRepo **/
        $highlightRepo = app(HighlightRepository::class);

        $languages = array_merge([$this->user->getMainLanguage()], $this->user->getLanguages());

        if (!$this->checkLite() && !$web && !checkNewApp()){
            //need to override language for current app (use languages from header)
            $request = app('request');
            $token = $request->header('nwtoken');
            $this->user = new FbUser($request, $token);
            $languages = array_merge([$this->user->getMainLanguage()], $this->user->getUserLanguages());
        }

        // Check if $language is set and not empty, then prepend it to $languages array
        if (!empty($language)) array_unshift($languages, $language);

        return $highlightRepo->getHighlight($highlightSlug, array_unique($languages));
    }

    /**
     * It gets the contents of a highlight feed
     *
     * @param user The user object
     * @param highlightSlug The slug of the highlight you want to get the contents for.
     * @param page The page number of the feed.
     * @param last The last content id that was fetched. This is used to fetch the next page of
     * contents.
     * @param contentType The type of content you want to get. This can be either 'article' or 'video' or both in array.
     */
    public function getHighightContents($user, $highlightSlug, $page, $last = null, $contentType){
        $this->user = $user;
        /**  @var HighlightRepository $highlightRepo **/
        $highlightRepo = app(HighlightRepository::class);
        $feedService = app(FeedService::class);
        /** @var ContentRepository */
        $contentRepo = app(ContentRepository::class);
        $languages = $this->user->getLanguages();
        $contents = $highlightRepo->getHighlightContents($highlightSlug, $languages, $contentType, $page, HIGHLIGHT_FEED_SIZE, $last);
        $contentIds = $contents->data;

        return [
            'content' => $user->isWeb() ? $feedService->injectWebAd($contentRepo->getContentBodyWeb($contentIds, 1, $highlightSlug, null, false)) : $feedService->injectAppAd($contentRepo->getContentBody($contentIds, 2, $highlightSlug, $languages), $page),
            'next_page' => ($contents->next_page_url) ? $page + 1 : "",
            'next_page_url' => $contents->next_page_url,
            'last' => isset($contents->last) ? $contents->last : null
        ];
    }

    public function getHighlightContentsV3($highlightSlug, $page, $last = null, $contentType){
        $request = app('request');
        $token = $request->header('nwtoken');
        $this->user = new FbUser($request, $token);
        /**  @var HighlightRepository $highlightRepo **/
        $highlightRepo = app(HighlightRepository::class);
        $languages = $this->user->getUserLanguages();
        $profileId = $this->user->getProfileId();
        $contents = $highlightRepo->getHighlightContents($highlightSlug, $languages, $contentType, $page, HIGHLIGHT_FEED_SIZE, $last);
        $contentIds = $contents->data;
        $articles = [];
        $videos = [];
        foreach ($contentIds as $content){
            if (like_match('A%_%', $content)){
                $articles[] = $content;
            }
            if (like_match('V%_%', $content)){
                $videos[] = $content;
            }
        }
        /** @var ArticleRepository $articleRepo */
        $articleRepo = app(ArticleRepository::class);
        $articles = count($articles) ? collect($articleRepo->getArticles($articles, false, true, [], true))->keyBy('uniqueID') : collect();

        /** @var VideoRepository $videoRepo */
        $videoRepo = app(VideoRepository::class);
        $videos = count($videos) ? $videoRepo->getVideosById($videos, $profileId)->keyBy('unique_id') : collect();
        $new = [];
        foreach ($contentIds as $content){
            if (like_match('A%_%', $content)){
                $new[] = $articles->get($content);
            }
            if (like_match('V%_%', $content)){
                $new[] = $videos->get($content);
            }
        }

        /**
         * For highlight type content, if the article id not exists/video id not exists,
         * $videos->get and $articles->get will return null
         *
         * need to filter this out so that the mobile app does not freak out
         */
        $new = collect($new)->filter(function ($item) {
            return !is_null($item);
        })->values();

        $response = [
            'newSession' => false,
            'more' => count($new) > 0 ? true : false,
            'next_page_url' => $contents->next_page_url ?? "",
            'prev_page_url' => $contents->prev_page_url ?? "",
            'articles' => $new
        ];
        return $response;
    }

    private function checkLite(){
        $request = app('request');
        if ($request->header('app') == "newswav-lite" || ($request->header('app') == 'newswav-android' && (int)$request->header('app-version') <= 5)){
            return true;
        }
        return false;
    }
}
